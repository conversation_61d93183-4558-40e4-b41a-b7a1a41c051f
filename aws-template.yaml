AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Static website
Parameters:
  ServiceName:
    Type: String
    Default: influencer-admin-panel

  EnvName:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The environment name.
    Default: /env/name

  SubDomainName:
    Type: String
    Description: The sub domain name to use
    Default: admin

  GithubToken:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The github token.
    Default: /codebuild/gh_token

Conditions:
  IsDev: !Equals [!Ref EnvName, dev]
  IsStg: !Equals [!Ref EnvName, stg]
  IsProd: !Equals [!Ref EnvName, prod]

Resources:
  # Create the S3 bucket where the files will be stored
  S3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
      OwnershipControls:
        Rules:
          - ObjectOwnership: ObjectWriter
      WebsiteConfiguration:
        IndexDocument: index.html
        ErrorDocument: 404.html
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
            AllowedOrigins:
              - "*"
            ExposedHeaders: []
            MaxAge: 3000
      Tags:
        - Key: CloudFormationStack
          Value: !Sub ${AWS::StackName}
        - Key: CloudFormationStackId
          Value: !Sub ${AWS::StackId}
        - Key: CloudFormationManaged
          Value: "true"

  S3BucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref S3Bucket
      PolicyDocument:
        Version: 2012-10-17
        Statement:
          - Action:
              - s3:GetObject
            Effect: Allow
            Resource: !Join
              - ""
              - - "arn:aws:s3:::"
                - !Ref S3Bucket
                - /*
            Principal: "*"
          - Action:
              - s3:ListBucket
            Effect: Allow
            Resource: !GetAtt S3Bucket.Arn
            Principal: "*"

  # CloudFront distribution for the Website
  Cloudfront:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Origins:
          - Id: S3Bucket
            DomainName: !Sub ${S3Bucket}.s3-website-${AWS::Region}.amazonaws.com
            CustomOriginConfig:
              OriginProtocolPolicy: http-only
          - Id: api
            DomainName: !ImportValue MAIN-API-DOMAIN-NAME
            CustomOriginConfig:
              OriginProtocolPolicy: https-only
              HTTPSPort: 443
              OriginSSLProtocols:
                - TLSv1.2
        Enabled: true
        Comment: Influencer admin app
        DefaultRootObject: ""
        Aliases:
          - !Sub
            - ${SubDomainName}.${EnvDomainName}
            - { EnvDomainName: !ImportValue MAIN-DOMAIN-NAME }
        DefaultCacheBehavior:
          TargetOriginId: S3Bucket
          ViewerProtocolPolicy: redirect-to-https
          CachePolicyId: 658327ea-f89d-4fab-a63d-7e88639e58f6 # CachingOptimized
          OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf # CORS-S3Origin
          ResponseHeadersPolicyId: 67f7725c-6f97-4210-82d7-5512b31e9d03 # Security headers
          Compress: true
          AllowedMethods:
            - GET
            - HEAD
            - OPTIONS
        CacheBehaviors:
          - PathPattern: /api/*
            AllowedMethods:
              - DELETE
              - GET
              - HEAD
              - OPTIONS
              - PATCH
              - POST
              - PUT
            TargetOriginId: api
            Compress: false
            ViewerProtocolPolicy: redirect-to-https
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad # Caching disabled
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac # AllViewerExceptHostHeader
            ResponseHeadersPolicyId: 67f7725c-6f97-4210-82d7-5512b31e9d03 # Security headers
        PriceClass: PriceClass_All
        ViewerCertificate:
          AcmCertificateArn: !ImportValue MAIN-ACM-CERTIFICATE-ARN
          SslSupportMethod: sni-only
          MinimumProtocolVersion: TLSv1.2_2021
      Tags:
        - Key: CloudFormationStack
          Value: !Sub ${AWS::StackName}
        - Key: CloudFormationStackId
          Value: !Sub ${AWS::StackId}
        - Key: CloudFormationManaged
          Value: "true"

  # The DNS record for the website
  WebsiteDNSRecordSet:
    Type: AWS::Route53::RecordSet
    Properties:
      HostedZoneId: !ImportValue MAIN-ROUTE53-HOSTED-ZONE-ID
      Comment: !Sub Website managed from cf-stack ${AWS::StackName}
      Type: CNAME
      TTL: "900"
      Name: !Sub
        - ${SubDomainName}.${EnvDomainName}
        - { EnvDomainName: !ImportValue MAIN-DOMAIN-NAME }
      ResourceRecords:
        - !GetAtt Cloudfront.DomainName

  # Create the CodeBuild project to build and deploy the source code
  CodeBuildProject:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: !Sub ${AWS::StackName}-build-deploy
      Description: !Sub Build and deploy the ${AWS::StackName} project
      ServiceRole: !ImportValue CI-CD-CodeBuildServiceRoleArn
      Source:
        Type: CODEPIPELINE
        BuildSpec: aws-buildspec.yaml
      Artifacts:
        Type: CODEPIPELINE
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_SMALL
        ImagePullCredentialsType: CODEBUILD
        Image: aws/codebuild/amazonlinux2-x86_64-standard:5.0
        EnvironmentVariables:
          - Name: ENV_NAME
            Value: !Ref EnvName
          - Name: APP_DOMAIN
            Value: !ImportValue MAIN-API-DOMAIN-NAME
          - Name: GITHUB_TOKEN
            Value: !Ref GithubToken
          - Name: DEPLOY_BUCKET
            Value: !Ref S3Bucket
          - Name: CLOUDFRONT_DISTRIBUTION
            Value: !Ref Cloudfront
      TimeoutInMinutes: 20

Outputs:
  InfluencerAppDomainName:
    Value: !Sub
      - ${SubDomainName}.${EnvDomainName}
      - { EnvDomainName: !ImportValue MAIN-DOMAIN-NAME }
    Export:
      Name: INFLUENCER-APP-DOMAIN-NAME
