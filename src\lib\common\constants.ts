export const EMAIL_REGEX_PATTERN =
  /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

export const LIST_MONTHS_NAMES = [
  "enero",
  "febrero",
  "marzo",
  "abril",
  "mayo",
  "junio",
  "julio",
  "agosto",
  "septiembre",
  "octubre",
  "noviembre",
  "diciembre",
];

export const LIST_WEEK_DAYS = ["do", "lu", "ma", "mi", "ju", "vi", "sa"];
export const STANDARD_FORMAT_DATE = "yyyy-MM-dd'T'HH:mm:ss";
export const TABLE_STANDARD_FORMAT_DATE = "dd/MM/yyyy hh:mm:ss a";
export const CARD_BRANDS = {
  Visa: "Visa",
  Amex: "Amex",
  Diners: "Diners",
  Mastercard: "Mastercard",
  Yape: "Yape",
  Default: "default",
};

export const countries = [
  { value: "", text: "Sin definir" },
  { value: "PE", text: "Perú" },
  { value: "ABH", text: "Abjasia" },
  { value: "AC", text: "Isla Ascensión" },
  { value: "AD", text: "Andorra" },
  { value: "AE", text: "Emiratos Árabes Unidos" },
  { value: "AF", text: "Afganistán" },
  { value: "AG", text: "Antigua y Barbuda" },
  { value: "AI", text: "Anguila" },
  { value: "AL", text: "Albania" },
  { value: "AM", text: "Armenia" },
  { value: "AO", text: "Angola" },
  { value: "AR", text: "Argentina" },
  { value: "AS", text: "Samoa Americana" },
  { value: "AT", text: "Austria" },
  { value: "AU", text: "Australia" },
  { value: "AW", text: "Aruba" },
  { value: "AZ", text: "Azerbaiyán" },
  { value: "BA", text: "Bosnia y Herzegovina" },
  { value: "BB", text: "Barbados" },
  { value: "BD", text: "Bangladesh" },
  { value: "BE", text: "Bélgica" },
  { value: "BF", text: "Burkina Faso" },
  { value: "BG", text: "Bulgaria" },
  { value: "BH", text: "Baréin" },
  { value: "BI", text: "Burundi" },
  { value: "BJ", text: "Benín" },
  { value: "BL", text: "Saint Barthélemy" },
  { value: "BM", text: "Bermudas" },
  { value: "BN", text: "Brunéi" },
  { value: "BO", text: "Bolivia" },
  { value: "BQ", text: "Bonaire, San Eustaquio y Saba" },
  { value: "BR", text: "Brasil" },
  { value: "BS", text: "Bután" },
  { value: "BT", text: "Bahamas" },
  { value: "BW", text: "Botsuana" },
  { value: "BY", text: "Bielorrusia" },
  { value: "BZ", text: "Belice" },
  { value: "CA", text: "Canadá" },
  { value: "CD", text: "República Democrática del Congo" },
  { value: "CF", text: "República Centroafricana" },
  { value: "CG", text: "República del Congo" },
  { value: "CH", text: "Suiza" },
  { value: "CI", text: "Costa de Marfil" },
  { value: "CK", text: "Islas Cook" },
  { value: "CL", text: "Chile" },
  { value: "CM", text: "Camerún" },
  { value: "CN", text: "China" },
  { value: "CO", text: "Colombia" },
  { value: "CR", text: "Costa Rica" },
  { value: "CU", text: "Cuba" },
  { value: "CV", text: "Cabo Verde" },
  { value: "CW", text: "Curazao" },
  { value: "CY", text: "Chipre" },
  { value: "CZ", text: "República Checa" },
  { value: "DE", text: "Alemania" },
  { value: "DJ", text: "Yibuti" },
  { value: "DK", text: "Dinamarca" },
  { value: "DM", text: "Dominica" },
  { value: "DO", text: "República Dominicana" },
  { value: "DZ", text: "Argelia" },
  { value: "EC", text: "Ecuador" },
  { value: "EE", text: "Estonia" },
  { value: "EG", text: "Egipto" },
  { value: "EH", text: "República Árabe Saharaui Democrática" },
  { value: "ER", text: "Eritrea" },
  { value: "ES", text: "España" },
  { value: "ET", text: "Etiopía" },
  { value: "FI", text: "Finlandia" },
  { value: "FJ", text: "Fiyi" },
  { value: "FK", text: "Islas Malvinas" },
  { value: "FM", text: "Micronesia" },
  { value: "FO", text: "Islas Feroe" },
  { value: "FR", text: "Francia" },
  { value: "GA", text: "Gabón" },
  { value: "GD", text: "Granada" },
  { value: "GE", text: "Georgia" },
  { value: "GF", text: "Guayana Francesa" },
  { value: "GH", text: "Ghana" },
  { value: "GI", text: "Gibraltar" },
  { value: "GL", text: "Groenlandia" },
  { value: "GM", text: "Gambia" },
  { value: "GN", text: "Guinea" },
  { value: "GP", text: "Guadalupe" },
  { value: "GQ", text: "Guinea Ecuatorial" },
  { value: "GR", text: "Grecia" },
  { value: "GT", text: "Guatemala" },
  { value: "GU", text: "Guam" },
  { value: "GW", text: "Guinea-Bisáu" },
  { value: "GY", text: "Guyana" },
  { value: "HK", text: "Hong Kong" },
  { value: "HN", text: "Honduras" },
  { value: "HR", text: "Croacia" },
  { value: "HT", text: "Haití" },
  { value: "HU", text: "Hungría" },
  { value: "ID", text: "Indonesia" },
  { value: "IE", text: "Irlanda" },
  { value: "IL", text: "Israel" },
  { value: "IN", text: "India" },
  { value: "IO", text: "Territorio Británico del Océano Índico" },
  { value: "IQ", text: "Irak" },
  { value: "IR", text: "Irán" },
  { value: "IS", text: "Islandia" },
  { value: "IT", text: "Italia" },
  { value: "JM", text: "Jamaica" },
  { value: "JO", text: "Jordania" },
  { value: "JP", text: "Japón" },
  { value: "KE", text: "Kenia" },
  { value: "KG", text: "Kirguistán" },
  { value: "KH", text: "Camboya" },
  { value: "KI", text: "Kiribati" },
  { value: "KM", text: "Comoras" },
  { value: "KN", text: "San Cristóbal y Nieves" },
  { value: "KP", text: "Corea del Norte" },
  { value: "KR", text: "Corea del Sur" },
  { value: "KW", text: "Kuwait" },
  { value: "KY", text: "Islas Caimán" },
  { value: "KZ", text: "Kazajistán" },
  { value: "LA", text: "Laos" },
  { value: "LB", text: "Líbano" },
  { value: "LC", text: "Santa Lucía" },
  { value: "LI", text: "Liechtenstein" },
  { value: "LK", text: "Sri Lanka" },
  { value: "LR", text: "Liberia" },
  { value: "LS", text: "Lesoto" },
  { value: "LT", text: "Lituania" },
  { value: "LU", text: "Luxemburgo" },
  { value: "LV", text: "Letonia" },
  { value: "LY", text: "Libia" },
  { value: "MA", text: "Marruecos" },
  { value: "MC", text: "Mónaco" },
  { value: "MD", text: "Moldavia" },
  { value: "ME", text: "Montenegro" },
  { value: "MG", text: "Madagascar" },
  { value: "MH", text: "Islas Marshall" },
  { value: "MK", text: "Macedonia del Norte" },
  { value: "ML", text: "Malí" },
  { value: "MM", text: "Birmania" },
  { value: "MN", text: "Mongolia" },
  { value: "MO", text: "Macao" },
  { value: "MP", text: "Islas Marianas del Norte" },
  { value: "MQ", text: "Martinica" },
  { value: "MR", text: "Mauritania" },
  { value: "MS", text: "Montserrat" },
  { value: "MT", text: "Malta" },
  { value: "MU", text: "Mauricio" },
  { value: "MV", text: "Maldivas" },
  { value: "MW", text: "Malaui" },
  { value: "MX", text: "México" },
  { value: "MY", text: "Malasia" },
  { value: "MZ", text: "Mozambique" },
  { value: "NA", text: "Namibia" },
  { value: "NC", text: "Nueva Caledonia" },
  { value: "NE", text: "Níger" },
  { value: "NF", text: "Isla Norfolk" },
  { value: "NG", text: "Nigeria" },
  { value: "NI", text: "Nicaragua" },
  { value: "NL", text: "Países Bajos" },
  { value: "NO", text: "Noruega" },
  { value: "NP", text: "Nepal" },
  { value: "NR", text: "Nauru" },
  { value: "NU", text: "Niue" },
  { value: "NZ", text: "Nueva Zelanda" },
  { value: "OM", text: "Omán" },
  { value: "PA", text: "Panamá" },
  { value: "PF", text: "Polinesia Francesa" },
  { value: "PG", text: "Papúa Nueva Guinea" },
  { value: "PH", text: "Filipinas" },
  { value: "PK", text: "Pakistán" },
  { value: "PL", text: "Polonia" },
  { value: "PM", text: "San Pedro y Miquelón" },
  { value: "PN", text: "Islas Pitcairn" },
  { value: "PR", text: "Puerto Rico" },
  { value: "PS", text: "Palestina" },
  { value: "PT", text: "Portugal" },
  { value: "PW", text: "Palaos" },
  { value: "PY", text: "Paraguay" },
  { value: "QA", text: "Catar" },
  { value: "RE", text: "Isla Reunión" },
  { value: "RO", text: "Rumania" },
  { value: "RS", text: "Serbia" },
  { value: "RU", text: "Rusia" },
  { value: "RW", text: "Ruanda" },
  { value: "SA", text: "Arabia Saudita" },
  { value: "SB", text: "Islas Salomón" },
  { value: "SC", text: "Seychelles" },
  { value: "SD", text: "Sudán" },
  { value: "SE", text: "Suecia" },
  { value: "SG", text: "Singapur" },
  { value: "SI", text: "Eslovenia" },
  { value: "SK", text: "Eslovaquia" },
  { value: "SL", text: "Sierra Leona" },
  { value: "SM", text: "San Marino" },
  { value: "SN", text: "Senegal" },
  { value: "SO", text: "Somalía" },
  { value: "SR", text: "Surinam" },
  { value: "SS", text: "Sudán del Sur" },
  { value: "ST", text: "Santo Tomé y Príncipe" },
  { value: "SV", text: "El Salvador" },
  { value: "SX", text: "San Martín" },
  { value: "SY", text: "Siria" },
  { value: "SZ", text: "Suazilandia" },
  { value: "TC", text: "Islas Turcas y Caicos" },
  { value: "TD", text: "Chad" },
  { value: "TG", text: "Togo" },
  { value: "TH", text: "Tailandia" },
  { value: "TJ", text: "Tayikistán" },
  { value: "TM", text: "Turkmenistán" },
  { value: "TN", text: "Túnez" },
  { value: "TO", text: "Tonga" },
  { value: "TR", text: "Turquía" },
  { value: "TT", text: "Trinidad y Tobago" },
  { value: "TV", text: "Tuvalu" },
  { value: "TW", text: "Taiwán" },
  { value: "TZ", text: "Tanzania" },
  { value: "UA", text: "Ucrania" },
  { value: "UG", text: "Uganda" },
  { value: "US", text: "Estados Unidos" },
  { value: "UY", text: "Uruguay" },
  { value: "UZ", text: "Uzbekistán" },
  { value: "VA", text: "Ciudad del Vaticano" },
  { value: "VE", text: "Venezuela" },
  { value: "VN", text: "Vietnam" },
  { value: "VU", text: "Vanuatu" },
  { value: "WS", text: "Samoa" },
  { value: "YE", text: "Yemen" },
  { value: "ZA", text: "Sudáfrica" },
  { value: "ZM", text: "Zambia" },
  { value: "ZW", text: "Zimbabue" },
];
