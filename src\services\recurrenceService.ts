import { BaseService } from "@app/lib/service/baseService";
import { GetRecurrenceResponse } from "@mainframe-peru/types/build/recurrence";

export class RecurrenceService extends BaseService {
  static async getUserRecurrenceSsr(
    userId: number | string,
    token?: string,
  ): Promise<GetRecurrenceResponse | undefined> {
    try {
      const { data: response } = await this.fetchData<GetRecurrenceResponse>(
        `${this.BASE_URL}/recurrence?userId=${userId}`,
        {
          headers: {
            Cookie: `session=${token}`,
          },
        },
      );
      return response;
    } catch {
      return undefined;
    }
  }

  static async getUserRecurrence(userId: number | string): Promise<GetRecurrenceResponse | undefined> {
    try {
      const { data: response } = await this.fetchData<GetRecurrenceResponse>(
        `${this.BASE_URL}/recurrence?userId=${userId}`,
        {
          headers: {
            Accept: "application/json",
          },
          credentials: "include",
        },
      );
      return response;
    } catch {
      return undefined;
    }
  }

  static async cancelUserRecurrence(userId: number | string) {
    await this.fetchData(`${this.BASE_URL}/recurrence?userId=${userId}`, {
      method: "DELETE",
      credentials: "include",
    });
  }
}
