"use client";

import { useEventsPage } from "@app/contexts/EventPageProvider";
import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { EditIcon } from "@chakra-ui/icons";
import { Box } from "@chakra-ui/react";
import { event } from "@mainframe-peru/types";
import { format } from "date-fns";
import Link from "next/link";
import { Table } from "../common/Table";
import { EventStatusTag } from "./EventStatusTag";
import { EventTypeTag } from "./EventTypeTag";
import { useSession } from "@app/hooks/useSession";

export function EventsTable() {
  const { events, isLoading } = useEventsPage();

  const makeRows = (events: event.GetEventResponse[]) => {
    return events.map((event) => {
      const { id, description, name, type, status, startDate, eventDate, endDate } = event;
      const eventDateFormated = eventDate ? format(new Date(eventDate), TABLE_STANDARD_FORMAT_DATE) : "";
      const startDateFormated = startDate ? format(new Date(startDate), TABLE_STANDARD_FORMAT_DATE) : "";
      const endDateFormated = endDate ? format(new Date(endDate), TABLE_STANDARD_FORMAT_DATE) : "";

      return [
        id,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {name}
        </Box>,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {description}
        </Box>,
        <EventTypeTag key={id} type={type} />,
        <EventStatusTag key={id} status={status} />,
        eventDateFormated,
        startDateFormated,
        endDateFormated,
        <EditRow key={id} id={id} />,
      ];
    });
  };

  return (
    <Table
      isLoading={isLoading}
      rows={makeRows(events)}
      boxProps={{ mt: 34 }}
      headers={[
        "Id",
        "Nombre",
        "Descripción",
        "Tipo",
        "Estado",
        "Fecha del evento",
        "Fecha de inicio",
        "Fecha de fin",
        "",
      ]}
    />
  );
}

function EditRow({ id }: { id: number }) {
  const session = useSession();
  return (
    <Link href={`/${session.admin?.influencerId}/app/premios-y-eventos/editar/?id=${id}`}>
      <EditIcon />
    </Link>
  );
}
