import { complaints } from "@app/components/common/Breadcrumb";
import { ComplaintsTable } from "@app/components/complaint/ComplaintsTable";
import { ComplaintsToolbar } from "@app/components/complaint/ComplaintsToolbar";
import { ComplaintsPageProvider } from "@app/contexts/ComplaintsPageProvider";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Quejas y Reclamos",
};

export default function ComplaintsPage() {
  return (
    <PageLayout breadcrumb={complaints} title="Quejas y Reclamos">
      <ComplaintsPageProvider>
        <ComplaintsToolbar />
        <ComplaintsTable />
      </ComplaintsPageProvider>
    </PageLayout>
  );
}
