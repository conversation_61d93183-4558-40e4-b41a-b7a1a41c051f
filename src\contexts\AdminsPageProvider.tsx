"use client";

import { AdminService } from "@app/services/adminService";
import { useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { admin } from "@mainframe-peru/types";
import React, { PropsWithChildren } from "react";

export type AdminProviderProps = PropsWithChildren;

type AdminsProviderValues = {
  admins: admin.ListAdminsResponse;
  isLoading: boolean;
  onSetIsLoading: (value: boolean) => void;
  onSetAdmin: (value: admin.ListAdminsResponse) => void;
};

export const AdminsContext = React.createContext({} as AdminsProviderValues);

export function useAdminsPage() {
  return React.useContext(AdminsContext);
}

export function AdminsPageProvider({ children }: AdminProviderProps) {
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [admins, setAdmins] = React.useState<admin.ListAdminsResponse>([]);
  const toast = useToast();
  const onSetIsLoading = (value: boolean) => {
    setIsLoading(value);
  };

  const onSetAdmin = (value: admin.ListAdminsResponse) => {
    setAdmins(value);
  };

  React.useEffect(() => {
    const fetchAdmins = async () => {
      try {
        setIsLoading(true);
        const admins = await AdminService.getAdmins({});
        setAdmins(admins);
      } catch (error) {
        if (error instanceof AppError) {
          setAdmins([]);
          toast({
            status: "error",
            title: "Error al mostrar los admins",
            description: "No se pudo mostrar los admins. Contacte a soporte.",
          });
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchAdmins();
  }, [toast]);

  return (
    <AdminsContext.Provider value={{ admins, isLoading, onSetAdmin, onSetIsLoading }}>
      {children}
    </AdminsContext.Provider>
  );
}
