"use client";

import { SearchIcon } from "@app/public/svgs";
import { Flex, FlexProps, Input, InputGroup, InputLeftElement, InputProps } from "@chakra-ui/react";
import React from "react";

const inputProps: InputProps = {
  color: "white",
  paddingLeft: 55,
  bg: "#404040",
  _placeholder: {
    color: "#A6A6A6",
  },
  pt: 5,
  pb: 5,
  border: "unset",
  fontWeight: "500",
  borderRadius: 21,
};

type SearchProps = {
  flexProps: FlexProps;
  placeholder: string;
  onSearch?: (search: string) => void | Promise<void>;
  onEmpty?: () => void | Promise<void>;
};

export const Search = React.forwardRef<HTMLInputElement, SearchProps>(
  ({ flexProps, onSearch, onEmpty, placeholder }, ref) => {
    const [search, setSearch] = React.useState("");

    const onSubmit = (evt: React.FormEvent<HTMLDivElement>) => {
      evt.preventDefault();
      if (!onSearch) return;
      onSearch(search);
    };

    const onInputEmpty = () => {
      if (!onEmpty) return;
      onEmpty();
    };

    const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
      if (evt.target.value.trim().length === 0) {
        onInputEmpty();
      }
      setSearch(evt.target.value);
    };

    return (
      <Flex onSubmit={onSubmit} as={"form"} {...flexProps}>
        <InputGroup>
          <InputLeftElement pointerEvents="none">
            <SearchIcon color={"black"} />
          </InputLeftElement>
          <Input
            {...inputProps}
            ref={ref}
            value={search}
            onChange={onChange}
            flexBasis={"100%"}
            placeholder={placeholder}
          />
        </InputGroup>
      </Flex>
    );
  },
);

Search.displayName = "Search";
