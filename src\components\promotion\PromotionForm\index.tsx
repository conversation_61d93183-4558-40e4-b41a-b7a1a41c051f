"use client";

import { But<PERSON> } from "@app/components/common/Button";
import { Flex, FormControl, FormLabel, Heading, Input, Skeleton, Stack, Textarea } from "@chakra-ui/react";
import { usePromotionForm } from "@app/hooks/usePromotionForm";
import { Option, Select } from "@app/components/common/Select";
import { common } from "@mainframe-peru/types";
import { useSearchParams } from "next/navigation";
import { CodePageProvider } from "@app/contexts/CodePageProvider";
import { CodeToolbar } from "@app/components/code/CodeToolbar";
import { CodesTable } from "@app/components/code/CodeTable";
import { ImageDropzone } from "@app/components/common/ImageDropzone";

const promotionTypes: Option[] = [
  { value: "GLOBAL", text: "Global" },
  { value: "PERSONAL", text: "Personal" },
];

const promotionStatus: Option[] = [
  { value: "ACTIVE", text: "Activo" },
  { value: "INACTIVE", text: "Inactivo" },
];

export function PromotionForm({ isEditing }: { isEditing?: boolean }) {
  const {
    promotion,
    businesses,
    isLoading,
    loadSkeleton,
    setPromotionImageFile,
    setPromotionCardImageFile,
    onSubmitPromotionForm,
    onChangePromotion,
  } = usePromotionForm();

  const id = useSearchParams().get("id");

  return (
    <Stack spacing={5}>
      <Stack as="form" gap={5} onSubmit={onSubmitPromotionForm} bg="black" color="white" borderRadius={21} p={5}>
        <Flex alignItems={"center"} justify={"space-between"}>
          <Heading size={"md"} color={"#5C73F2"}>
            Datos de la Promoción
          </Heading>
        </Flex>

        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>Nombre</FormLabel>
            <Input
              value={promotion.name}
              onChange={(e) => onChangePromotion({ name: e.target.value })}
              placeholder="Ingresa el nombre de la promoción"
              bgColor={"gray.500"}
            />
          </FormControl>
        </Skeleton>

        <Flex gap={3} flexDir={{ base: "column", lg: "row" }} w="100%" justifyContent={"space-between"}>
          <Skeleton borderRadius={21} isLoaded={!loadSkeleton} flex="1">
            <FormControl>
              <FormLabel fontSize={13}>Negocio</FormLabel>
              <Select
                options={businesses.map((x) => ({ value: x.id.toString(), text: x.name }))}
                onChange={(e) => onChangePromotion({ businessId: Number(e) })}
                value={promotion.businessId?.toString() || ""}
                selectButtonProps={{ w: "100%", bg: "gray.500" }}
              ></Select>
            </FormControl>
          </Skeleton>

          <Skeleton borderRadius={21} isLoaded={!loadSkeleton} flex="1">
            <FormControl>
              <FormLabel fontSize={13}>Tipo</FormLabel>
              <Select
                options={promotionTypes}
                onChange={(e) => onChangePromotion({ type: e as common.BusinessPromotionType })}
                value={promotion.type?.toString() || ""}
                selectButtonProps={{ w: "100%", bg: "gray.500" }}
              ></Select>
            </FormControl>
          </Skeleton>

          <Skeleton borderRadius={21} isLoaded={!loadSkeleton} flex="1">
            <FormControl>
              <FormLabel fontSize={13}>Estado</FormLabel>
              <Select
                options={promotionStatus}
                onChange={(e) => onChangePromotion({ status: e as common.BusinessPromotionStatus })}
                value={promotion.status?.toString() || ""}
                selectButtonProps={{ w: "100%", bg: "gray.500" }}
              ></Select>
            </FormControl>
          </Skeleton>
        </Flex>

        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>Valor</FormLabel>
            <Input
              value={promotion.value}
              onChange={(e) => onChangePromotion({ value: e.target.value })}
              placeholder="Ingresa el valor de la promoción (ej. 20% descuento, S/15 soles)"
              bgColor={"gray.500"}
            />
          </FormControl>
        </Skeleton>

        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>Descripción</FormLabel>
            <Textarea
              value={promotion.description}
              onChange={(e) => onChangePromotion({ description: e.target.value })}
              placeholder="Ingresa la descripción de la promoción"
              resize="none"
              rows={5}
              bgColor={"gray.500"}
            />
          </FormControl>
        </Skeleton>

        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>Contenido</FormLabel>
            <Textarea
              value={promotion.content}
              onChange={(e) => onChangePromotion({ content: e.target.value })}
              placeholder="Ingresa el contenido de la promoción"
              resize="none"
              rows={5}
              bgColor={"gray.500"}
            />
          </FormControl>
        </Skeleton>
        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>Imagen de la promocion</FormLabel>
            <ImageDropzone
              onDrop={(files) => {
                setPromotionImageFile(files[0]);
                onChangePromotion({ ...promotion, imageUrl: URL.createObjectURL(files[0]) });
              }}
              previewUrl={promotion.imageUrl || ""}
            />
          </FormControl>
        </Skeleton>
        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>Imagen de la promocion en la tarjeta</FormLabel>
            <ImageDropzone
              onDrop={(files) => {
                setPromotionCardImageFile(files[0]);
                onChangePromotion({ ...promotion, cardImageUrl: URL.createObjectURL(files[0]) });
              }}
              previewUrl={promotion.cardImageUrl || ""}
            />
          </FormControl>
        </Skeleton>
        <Flex justifyContent={"center"}>
          <Button type="submit" isLoading={isLoading} width={{ base: "100%", md: "40%", lg: "25%" }}>
            {isEditing ? "Actualizar" : "Crear"} promoción
          </Button>
        </Flex>
      </Stack>
      {id && (
        <Stack bg="black" color="white" borderRadius={21} p={5}>
          <CodePageProvider promotionId={Number(id)}>
            <CodeToolbar promotionId={Number(id)} />
            <CodesTable />
          </CodePageProvider>
        </Stack>
      )}
    </Stack>
  );
}
