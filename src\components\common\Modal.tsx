import {
  Modal as ChakraModal,
  ModalProps as ChakraModalProps,
  ModalBody,
  ModalBodyProps,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  <PERSON>dalHeader,
  ModalOverlay,
} from "@chakra-ui/react";

type ModalProps = {
  title?: string;
  modalProps: Omit<ChakraModalProps, "children">;
  footerContent?: React.ReactNode;
  children: React.ReactNode;
  bodyProps?: ModalBodyProps;
  closable?: boolean;
};

export function Modal({ children, footerContent, modalProps, title, bodyProps, closable }: ModalProps) {
  return (
    <ChakraModal isCentered {...modalProps} size="auto">
      <ModalOverlay />
      <ModalContent bgColor={"gray.400"} maxW={{ base: "90%", md: "fit-content" }}>
        {title && <ModalHeader>{title}</ModalHeader>}
        {closable && <ModalCloseButton />}
        <ModalBody {...bodyProps}>{children}</ModalBody>
        {footerContent && <ModalFooter>{footerContent}</ModalFooter>}
      </ModalContent>
    </ChakraModal>
  );
}
