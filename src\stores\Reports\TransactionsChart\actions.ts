import { TransactionsChartChangeDateAction, TransactionsChartLoadAction, TransactionsChartStore } from "./types";

export function loadTransactionsReport(
  action: TransactionsChartLoadAction,
  state: TransactionsChartStore,
): TransactionsChartStore {
  const transactions = action.payload.transactions || [];

  return {
    ...state,
    transactions,
    isLoading: action.payload.isLoading,
  };
}

export function changeDate(
  action: TransactionsChartChangeDateAction,
  state: TransactionsChartStore,
): TransactionsChartStore {
  const [[target, value]] = Object.entries(action.payload);
  return {
    ...state,
    filters: {
      ...state.filters,
      date: {
        ...state.filters.date,
        [target]: value,
      },
    },
  };
}
