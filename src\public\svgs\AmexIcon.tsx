import { Box, BoxProps } from "@chakra-ui/react";

export function AmexIcon({ width = "58px", height = "40px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg width="100%" height="100%" viewBox="0 0 58 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.5" y="0.5" width="57" height="39" rx="5.5" fill="none" />
        <rect x="0.5" y="0.5" width="57" height="39" rx="5.5" stroke="#F9F7F6" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.159 14.1667L4.85715 26.2446H11.2042L11.9911 24.3189H13.7896L14.5765 26.2446H21.5628V24.7748L22.1853 26.2446H25.7992L26.4217 24.7437V26.2446H40.9514L42.7181 24.3689L44.3724 26.2446L51.8352 26.2601L46.5166 20.2393L51.8352 14.1667H44.4882L42.7684 16.0077L41.1661 14.1667H25.3598L24.0025 17.2841L22.6133 14.1667H16.2795V15.5864L15.5749 14.1667H10.159ZM11.3871 15.8817H14.481L17.9977 24.0718V15.8817H21.3869L24.1031 21.754L26.6065 15.8817H29.9787V24.5484H27.9268L27.91 17.7573L24.9185 24.5484H23.083L20.0747 17.7573V24.5484H15.8534L15.0531 22.6055H10.7294L9.93082 24.5467H7.66909L11.3871 15.8817ZM40.1997 15.8817H31.8561V24.5433H40.0705L42.7181 21.6727L45.2701 24.5433H47.9377L44.0603 20.2376L47.9377 15.8817H45.3858L42.7516 18.7194L40.1997 15.8817ZM12.8922 17.3481L11.4677 20.8093H14.315L12.8922 17.3481ZM33.9165 19.2583V17.6762V17.6747H39.1227L41.3943 20.2048L39.022 22.7489H33.9165V21.0217H38.4683V19.2583H33.9165Z"
          fill="white"
        />
      </svg>
    </Box>
  );
}
