import { BaseService } from "@app/lib/service/baseService";
import { user } from "@mainframe-peru/types";
import {
  GetUserResponse,
  ListUsersResponse,
  UpdateUserRequest,
  UpdateUserResponse,
} from "@mainframe-peru/types/build/user";

export class UserService extends BaseService {
  /**
   * Obtiene una lista de usuarios con la opción de filtrar por nombre, DNI o email.
   */
  static async getUsers(query: user.ListUsersRequest): Promise<ListUsersResponse> {
    const url = `${this.BASE_URL}/user/list-users?${this.buildQueryParams(query)}`;
    const { data } = await this.fetchData<ListUsersResponse>(url, {
      headers: {
        Accept: "application/json",
      },
    });
    return data;
  }

  static async updateUser(user: UpdateUserRequest) {
    const url = `${this.BASE_URL}/user/`;
    const { data } = await this.fetchData<UpdateUserResponse>(url, {
      method: "PUT",
      credentials: "include",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(user),
    });
    return data;
  }

  /**
   * Obtiene una lista de usuarios con la opción de filtrar por nombre, DNI o email.
   * en formato csv
   */
  static async getUsersCsv(query: user.ListUsersRequest): Promise<void> {
    const url = `${this.BASE_URL}/user/list-users?${this.buildQueryParams(query)}`;
    const { data, headers } = await this.fetchData<string>(url, {
      headers: {
        Accept: "text/csv",
      },
    });
    this.downloadCsv(data, headers!.get("Content-Disposition"));
  }

  /**
   * Obtiene un usuario por su ID en un entorno SSR.
   */
  static async getUserByIdSsr(userId: string | number, token?: string): Promise<GetUserResponse | undefined> {
    const url = `${this.BASE_URL}/user?id=${userId}`;
    const headers = { Cookie: `session=${token}` };
    const { data } = await this.fetchData<GetUserResponse>(url, { headers });
    return data;
  }

  /**
   * Obtiene un usuario por su ID
   */
  static async getUserById(userId: string | number): Promise<GetUserResponse> {
    const url = `${this.BASE_URL}/user?id=${userId}`;
    const { data } = await this.fetchData<GetUserResponse>(url, {
      headers: { Accept: "applictaion/json" },
      credentials: "include",
    });
    return data;
  }
}
