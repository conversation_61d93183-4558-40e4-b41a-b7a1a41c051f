{"name": "influencer-admin-panel", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier . --write", "format:check": "prettier . --check", "prepare": "husky"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@mainframe-peru/common-core": "^1.25.2", "@mainframe-peru/types": "^1.107.0", "date-fns": "^4.1.0", "next": "14.2.12", "react": "18", "react-dom": "18", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.0", "recharts": "^2.13.3"}, "devDependencies": {"@types/node": "20", "@types/papaparse": "^5.3.15", "@types/react": "18", "@types/react-dom": "18", "eslint": "8", "eslint-config-next": "14.2.12", "husky": "9.1.6", "prettier": "3.3.3", "typescript": "5"}}