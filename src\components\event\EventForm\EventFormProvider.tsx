"use client";

import { useSession } from "@app/hooks/useSession";
import { useToast } from "@app/hooks/useToast";
import { EventService } from "@app/services/eventService";
import { StorageService } from "@app/services/storageService";
import { AppError } from "@mainframe-peru/common-core";
import { common, event } from "@mainframe-peru/types";
import { set } from "date-fns";
import { useSearchParams } from "next/navigation";
import React from "react";

type EventFormContextValues = {
  event: Event;
  isLoading: boolean;
  isSeasonEvent: boolean;
  loadSkeleton: boolean;
  onSubmitEventForm: (e: React.FormEvent<HTMLDivElement>) => void;
  onChangeEvent: (e: Partial<Event>) => void;
  onDrop: (acceptedFiles: File[]) => void;
  participationForm: React.MutableRefObject<common.AttributeKey[]>;
};

type EventFormProviderProps = {
  children: React.ReactNode;
};

export const defaultDate = set(new Date(), { hours: 0, minutes: 0, seconds: 0 });
export type Event = event.CreateEventRequest;
export const EventFormContext = React.createContext({} as EventFormContextValues);

export function EventFormProvider({ children }: EventFormProviderProps) {
  const id = useSearchParams().get("id");
  const session = useSession();
  const [event, setEvent] = React.useState<Event>({
    name: "",
    description: "",
    influencerId: session.admin?.influencerId || "",
    status: "ACTIVE",
    type: "PRIZE",
    eventDate: defaultDate,
    startDate: defaultDate,
    endDate: defaultDate,
    imageUrl: "",
    targetUser: "SUBSCRIBED",
  });
  const participationForm = React.useRef<common.AttributeKey[]>([]);

  const imageFile = React.useRef<File>();
  const isSeasonEvent = React.useMemo(() => ["IN-PERSON", "GAME"].includes(event.type), [event.type]);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [loadSkeleton, setLoadSkeleton] = React.useState<boolean>(!!id);
  const { fire } = useToast();

  const onChangeEvent = (e: Partial<Event>) => {
    setEvent((prev) => ({ ...prev, ...e }));
  };

  const onDrop = React.useCallback(
    (acceptedFiles: File[]) => {
      imageFile.current = acceptedFiles[0];
      setEvent((prevEvent) => ({
        ...prevEvent,
        imageUrl: URL.createObjectURL(acceptedFiles[0]),
      }));
    },
    [fire],
  );

  const onSubmitEventForm = async (e: React.FormEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsLoading(true);

    if (!id && !imageFile.current) {
      fire("Error de validación", "Debes subir una imagen antes de enviar el formulario.", "error");
      setIsLoading(false);
      return;
    }

    if (event.type !== "PRODUCT" && !EventService.validateDatesOnEvent(event)) {
      fire("Error de validación", "Las fechas son inválidas.", "error");
      setIsLoading(false);
      return;
    }

    if ([event.name, event.description].includes("")) {
      fire("Error de validación", "Debes de colocar un titulo y una descripción para crear el evento.", "error");
      setIsLoading(false);
      return;
    }

    let imageUrl: string | undefined = undefined;
    if (imageFile.current) {
      const uploadResponse = await StorageService.upload({
        file: imageFile.current,
        folderName: "events",
      });
      imageUrl = uploadResponse.imageUrl;
    }

    const { description, name, influencerId, status, type, endDate, eventDate, startDate } = event;
    let toastDescription = "Evento Creado Correctamente";
    try {
      if (!id) {
        // esta creando un evento nuevo
        await EventService.createEvent({
          name,
          description,
          influencerId,
          status,
          type,
          endDate,
          eventDate,
          startDate,
          imageUrl: imageUrl || "",
          participationForm: participationForm.current,
          targetUser: "SUBSCRIBED",
        });
      } else {
        // esta editando un evento existente
        toastDescription = "Evento Editado Correctamente";
        await EventService.updateEvent({
          id: parseInt(id),
          description,
          name,
          influencerId,
          status,
          type,
          endDate,
          eventDate,
          startDate,
          imageUrl,
          participationForm: participationForm.current,
        });
      }
      imageFile.current = undefined;
      fire("Acción realizada con éxito", toastDescription, "success");
    } catch (error) {
      if (error instanceof AppError) {
        fire(error.code, error.message, "success");
      }
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    if (!id) return;

    const fetchEvent = async () => {
      try {
        const event = await EventService.getEvent(id);
        const { description, imageUrl, influencerId, name, status, type, endDate, eventDate, startDate } = event;
        participationForm.current = event.participationForm || [];
        setEvent({
          description,
          imageUrl,
          influencerId,
          name,
          status,
          type,
          endDate: !endDate ? defaultDate : new Date(endDate),
          targetUser: "SUBSCRIBED",
          startDate: !startDate ? defaultDate : new Date(startDate),
          eventDate: !eventDate ? defaultDate : new Date(eventDate),
        });
        setLoadSkeleton(false);
      } catch (error) {
        if (error instanceof AppError) {
          fire(error.code, error.message, "error");
          setLoadSkeleton(false);
        }
      }
    };
    fetchEvent();
  }, [id, fire]);

  return (
    <EventFormContext.Provider
      value={{
        event,
        isLoading,
        loadSkeleton,
        isSeasonEvent,
        onSubmitEventForm,
        onChangeEvent,
        onDrop,
        participationForm,
      }}
    >
      {children}
    </EventFormContext.Provider>
  );
}
