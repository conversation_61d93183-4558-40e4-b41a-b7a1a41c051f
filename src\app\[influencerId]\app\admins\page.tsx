import { PageLayout } from "@app/layouts/PageLayout";
import { admins } from "@app/components/common/Breadcrumb";
import React from "react";
import { AdminsPageProvider } from "@app/contexts/AdminsPageProvider";
import { AdminsTable } from "@app/components/admin/AdminsTable";
import { AdminToolbar } from "@app/components/admin/AdminToolbar";

export default function UserPage() {
  return (
    <PageLayout breadcrumb={admins} title="Admins">
      <AdminsPageProvider>
        <AdminToolbar />
        <AdminsTable />
      </AdminsPageProvider>
    </PageLayout>
  );
}
