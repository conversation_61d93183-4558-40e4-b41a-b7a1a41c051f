import { ButtonProps } from "@chakra-ui/react";
import { Button } from "./Button";

const linearGradientGreenToBlue = "linear-gradient(90deg, #00FF75 0%, #24ADFA 100%)";
const buttonProps: ButtonProps = {
  height: "fit-content",
  py: "5px",
  fontSize: "12px",
  px: "8px",
  color: "white.400",
  borderRadius: "25px",
  bg: "gray.300",
  _hover: {
    color: "black",
    bg: linearGradientGreenToBlue,
    cursor: "pointer",
  },
};

export type CheckButtonProps = {
  isActive: boolean;
  text: string;
  onClick?: () => void;
};

export function CheckButton({ isActive, text, onClick }: CheckButtonProps) {
  const bgColor = isActive ? linearGradientGreenToBlue : "gray.300";
  return (
    <Button {...buttonProps} bgImage={bgColor} color={isActive ? "black" : "white"} onClick={onClick}>
      {text}
    </Button>
  );
}
