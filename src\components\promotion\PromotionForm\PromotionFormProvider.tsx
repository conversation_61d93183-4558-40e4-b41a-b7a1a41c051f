"use client";

import React, { createContext, useState } from "react";
import { business, businessPromotion } from "@mainframe-peru/types";
import { BusinessService } from "@app/services/businessService";
import { useToast } from "@app/hooks/useToast";
import { AppError } from "@mainframe-peru/common-core";
import { useRouter, useSearchParams } from "next/navigation";
import { PromotionService } from "@app/services/promotionService";
import { useSession } from "@app/hooks/useSession";
import { StorageService } from "@app/services/storageService";

export type Promotion = businessPromotion.CreateBusinessPromotionRequest;

interface PromotionFormContextProps {
  promotion: Partial<Promotion>;
  businesses: business.ListBusinessResponse;
  isLoading: boolean;
  loadSkeleton: boolean;
  promotionImageFile: File | null;
  setPromotionImageFile: React.Dispatch<React.SetStateAction<File | null>>;
  promotionCardImageFile: File | null;
  setPromotionCardImageFile: React.Dispatch<React.SetStateAction<File | null>>;
  onChangePromotion: (values: Partial<Promotion>) => void;
  onSubmitPromotionForm: (e: React.FormEvent) => void;
}

export const PromotionFormContext = createContext<PromotionFormContextProps>({} as PromotionFormContextProps);

export function PromotionFormProvider({ children }: { children: React.ReactNode }) {
  const [promotion, setPromotion] = useState<Promotion>({
    name: "",
    description: "",
    businessId: 0,
    content: "",
    imageUrl: "",
    cardImageUrl: "",
    expirationDate: new Date(),
    type: "GLOBAL",
    value: "",
    status: "ACTIVE",
  });

  const [promotionImageFile, setPromotionImageFile] = useState<File | null>(null);
  const [promotionCardImageFile, setPromotionCardImageFile] = useState<File | null>(null);

  const [businesses, setBusinesses] = useState<business.ListBusinessResponse>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadSkeleton, setLoadSkeleton] = useState(true);
  const id = useSearchParams().get("id");
  const businessId = useSearchParams().get("businessId");
  const router = useRouter();
  const { influencer } = useSession();

  const { fire } = useToast();

  const onChangePromotion = (values: Partial<Promotion>) => {
    setPromotion((prev) => ({ ...prev, ...values }));
  };

  const onSubmitPromotionForm = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    const { businessId, description, name, type, value, content, expirationDate, status } = promotion;
    try {
      if (!id) {
        if (!promotionImageFile) {
          fire("Error de formulario", "El campo de imagen es requerido", "error");
          return;
        }

        if (!promotionCardImageFile) {
          fire("Error de formulario", "El campo de imagen es requerido", "error");
          return;
        }

        const [imgRes, cardImgRes] = await Promise.allSettled([
          StorageService.upload({
            file: promotionImageFile,
            folderName: "business-promotions",
          }),
          StorageService.upload({
            file: promotionCardImageFile,
            folderName: "business-promotions",
          }),
        ]);

        if (imgRes.status !== "fulfilled" || cardImgRes.status !== "fulfilled") {
          fire("Error de subida", "No se pudieron subir las imágenes", "error");
          return;
        }

        const imageUrl = imgRes.value.imageUrl;
        const cardImageUrl = cardImgRes.value.imageUrl;

        await PromotionService.createPromotion({
          name,
          description,
          businessId,
          content,
          imageUrl,
          cardImageUrl,
          type,
          expirationDate,
          value,
          status,
        });

        fire("¡Éxito!", "La promoción se creó correctamente.", "success");
        router.push(`/${influencer.id}/app/promociones/negocios/editar/?id=${businessId}`);
      } else {
        const payload: businessPromotion.UpdateBusinessPromotionRequest = {
          id: Number(id),
          name,
          description,
          content,
          type,
          expirationDate,
          value,
          status,
        };

        const uploads: Promise<unknown>[] = [];

        if (promotionImageFile) {
          uploads.push(
            StorageService.upload({
              file: promotionImageFile,
              folderName: "business-promotions",
            }).then((res) => (payload.imageUrl = res.imageUrl)),
          );
        }

        if (promotionCardImageFile) {
          uploads.push(
            StorageService.upload({
              file: promotionCardImageFile,
              folderName: "business-promotions",
            }).then((res) => (payload.cardImageUrl = res.imageUrl)),
          );
        }

        if (uploads.length) {
          const results = await Promise.allSettled(uploads);
          const hasError = results.some((r) => r.status === "rejected");
          if (hasError) {
            fire("Error de subida", "No se pudieron subir una o más imágenes", "error");
            return;
          }
        }

        await PromotionService.updatePromotion(payload);
        fire("¡Éxito!", "La promoción se actualizó correctamente.", "success");
      }
    } catch (error) {
      if (error instanceof AppError) {
        fire(error.code, error.message, "error");
      }
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    const fetchBusinessses = async () => {
      try {
        setBusinesses(await BusinessService.getBusinesses({}));
        if (businessId) {
          onChangePromotion({ businessId: Number(businessId) });
        }

        if (id) {
          const promotion = await PromotionService.getPromotion(id);
          setPromotion({
            businessId: promotion.business.id,
            imageUrl: promotion.imageUrl,
            cardImageUrl: promotion.cardImageUrl,
            content: promotion.content,
            expirationDate: promotion.expirationDate,
            name: promotion.name,
            type: promotion.type,
            value: promotion.value,
            description: promotion.description,
            status: promotion.status,
          });
        }
        setLoadSkeleton(false);
      } catch (error) {
        if (error instanceof AppError) {
          fire(error.code, error.message, "error");
          setLoadSkeleton(false);
        }
      }
    };

    fetchBusinessses();
  }, [fire, id, businessId]);

  return (
    <PromotionFormContext.Provider
      value={{
        promotion,
        businesses,
        isLoading,
        loadSkeleton,
        promotionImageFile,
        setPromotionImageFile,
        promotionCardImageFile,
        setPromotionCardImageFile,
        onChangePromotion,
        onSubmitPromotionForm,
      }}
    >
      {children}
    </PromotionFormContext.Provider>
  );
}
