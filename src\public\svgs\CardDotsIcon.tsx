import { Box, BoxProps } from "@chakra-ui/react";

export function CardDotsIcon({ width = "147px", height = "10px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 147 10" fill="none">
        <circle cx="1.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="7.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="13.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="19.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="25.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="31.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="37.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="43.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="49.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="55.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="61.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="67.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="73.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="79.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="85.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="91.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="97.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="103.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="109.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="115.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="121.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="127.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="133.5" cy="2" r="1.5" fill="#FFC939" />
        <circle cx="139.5" cy="8" r="1.5" fill="#FFC939" />
        <circle cx="145.5" cy="2" r="1.5" fill="#FFC939" />
      </svg>
    </Box>
  );
}
