"use client";

import { useSession } from "@app/hooks/useSession";
import { PromotionService } from "@app/services/promotionService";
import { useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { businessPromotion } from "@mainframe-peru/types";
import React, { PropsWithChildren } from "react";

export type PromotionProviderProps = PropsWithChildren;

export type PromotionPageFilters = {
  businessId?: number;
};

type PromotionProviderValues = {
  promotions: businessPromotion.CreateBusinessPromotionResponse[];
  isLoading: boolean;
  businessId?: number;
};

export const PromotionContext = React.createContext({} as PromotionProviderValues);

export function usePromotionPage() {
  return React.useContext(PromotionContext);
}

export function PromotionPageProvider({ children, businessId }: PromotionProviderProps & { businessId?: number }) {
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [promotions, setPromotions] = React.useState<businessPromotion.ListBusinessPromotionResponse>([]);
  const { influencer } = useSession();
  const toast = useToast();
  const session = useSession();

  React.useEffect(() => {
    const fetchPromotions = async () => {
      setIsLoading(true);
      try {
        const promotions = await PromotionService.getPromotions({ businessId, influencerId: influencer.id });
        setPromotions(promotions);
      } catch (error) {
        if (error instanceof AppError) {
          setPromotions([]);
          toast({
            status: "error",
            title: "Error al mostrar las promociones",
            description: "No se pudo mostrar las promociones, contacte a soporte",
          });
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchPromotions();
  }, [businessId, toast, session.admin?.influencerId]);

  return (
    <PromotionContext.Provider value={{ promotions, isLoading, businessId }}>{children}</PromotionContext.Provider>
  );
}
