"use client";

import { useSession } from "@app/hooks/useSession";
import { BusinessService } from "@app/services/businessService";
import { useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { business } from "@mainframe-peru/types";
import React, { PropsWithChildren } from "react";

export type BusinessProviderProps = PropsWithChildren;

type BusinessProviderValues = {
  businesses: business.CreateBusinessResponse[];
  isLoading: boolean;
};

export const BusinessContext = React.createContext({} as BusinessProviderValues);

export function useBusinessPage() {
  return React.useContext(BusinessContext);
}

export function BusinessPageProvider({ children }: BusinessProviderProps) {
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [businesses, setBusinesses] = React.useState<business.ListBusinessResponse>([]);
  const toast = useToast();

  const session = useSession();

  React.useEffect(() => {
    const fetchBusinesses = async () => {
      setIsLoading(true);
      try {
        const businesses = await BusinessService.getBusinesses({});
        setBusinesses(businesses);
      } catch (error) {
        if (error instanceof AppError) {
          setBusinesses([]);
          toast({
            status: "error",
            title: "Error al mostrar los negocios",
            description: "No se pudo mostrar los negocios, contacte a soporte",
          });
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchBusinesses();
  }, [toast, session.admin?.influencerId]);

  return <BusinessContext.Provider value={{ businesses, isLoading }}>{children}</BusinessContext.Provider>;
}
