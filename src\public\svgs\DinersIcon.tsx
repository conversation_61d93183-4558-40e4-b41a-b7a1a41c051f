import { Box, BoxProps } from "@chakra-ui/react";

export function DinersIcon({ width = "30px", height = "25px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 30 25" fill="none">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.3974 24.1666H16.7242C23.4782 24.1666 28.8604 18.8083 29.0714 12.1891V12.0841C28.9659 5.35984 23.4782 -0.103591 16.7242 0.00147516H11.8697C5.43226 0.211607 0.366726 5.67504 0.57779 12.1891C0.57779 18.7032 5.85439 24.0616 12.3974 24.1666ZM2.68843 12.084C2.68843 6.62058 7.22631 2.10275 12.714 2.10275C18.2016 2.10275 22.7395 6.62058 22.7395 12.084C22.7395 17.5474 18.2016 22.0653 12.714 22.0653C7.22631 22.0653 2.68843 17.5474 2.68843 12.084ZM14.297 18.8082V5.46487C17.9906 6.41046 20.3123 10.0878 19.3625 13.7651C18.7293 16.2867 16.8297 18.1779 14.297 18.8082ZM6.17099 10.403C5.2212 14.0803 7.43737 17.8627 11.131 18.8082V5.46487C8.70376 6.09526 6.80418 7.98645 6.17099 10.403Z"
          fill="#ffff"
        />
      </svg>
    </Box>
  );
}
