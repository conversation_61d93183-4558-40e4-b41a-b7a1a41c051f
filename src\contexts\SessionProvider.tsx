"use client";

import { Loader } from "@app/components/common/Loader";
import { sessionExpired } from "@app/lib/auth/utils";
import { adminPoliciesConstant } from "@mainframe-peru/common-core";
import { admin, influencer } from "@mainframe-peru/types";
import { LoginAdminResponse } from "@mainframe-peru/types/build/admin";
import { InfluencerService } from "@services/influencerService";
import React from "react";

export type SessionProviderValues = {
  influencer: influencer.GetInfluencerResponse;
  admin: admin.GetAdminResponse | undefined;
  isInPolicy: <M extends keyof typeof adminPoliciesConstant, P extends keyof (typeof adminPoliciesConstant)[M]>(
    module: M,
    policy: P,
  ) => boolean;
};

export const SessionContext = React.createContext<SessionProviderValues>({} as SessionProviderValues);

type ClientSessionProviderProps = {
  children: React.ReactNode;
  influencerId: string;
};

export function SessionProvider({ children, influencerId }: ClientSessionProviderProps) {
  const isClient = typeof window !== "undefined";
  const admin: LoginAdminResponse = React.useMemo(() => {
    if (isClient) {
      return JSON.parse(localStorage.getItem("admin") || "null");
    }
  }, [isClient]);
  const [influencer, setInfluencer] = React.useState<influencer.GetInfluencerResponse>();
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const isInPolicy = <M extends keyof typeof adminPoliciesConstant, P extends keyof (typeof adminPoliciesConstant)[M]>(
    module: M,
    policy: P,
  ): boolean => {
    return !!admin?.policies && !!(admin.policies[module] & adminPoliciesConstant[module][policy]?.value);
  };

  React.useEffect(() => {
    if (sessionExpired() && isClient) {
      window.location.href = `/${influencerId}/login`;
      return;
    }

    const fetchData = async () => {
      const influencer = await InfluencerService.getInfluencer(influencerId);
      setInfluencer(influencer);
      setIsLoading(false);
    };

    fetchData();
  }, [admin, isClient, influencerId]);

  return (
    <SessionContext.Provider value={{ influencer: influencer as influencer.GetInfluencerResponse, admin, isInPolicy }}>
      {!isLoading ? children : <Loader />}
    </SessionContext.Provider>
  );
}
