import { Event } from "@app/components/event/EventForm/EventFormProvider";
import { BaseService } from "@app/lib/service/baseService";
import { event } from "@mainframe-peru/types";

export class EventService extends BaseService {
  static async getEvents(query: Partial<event.ListEventsRequest>): Promise<event.ListEventsResponse> {
    const url = `${this.BASE_URL}/event/list-events?${this.buildQueryParams(query)}`;
    const { data } = await this.fetchData<event.ListEventsResponse>(url, { credentials: "include" });
    return data;
  }

  static async createEvent(payload: event.CreateEventRequest): Promise<event.CreateEventResponse> {
    const { data } = await this.fetchData<event.CreateEventResponse>(`${this.BASE_URL}/event/`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    return data;
  }

  static async getEvent(id: string) {
    const { data } = await this.fetchData<event.GetEventResponse>(
      `${this.BASE_URL}/event?${this.buildQueryParams({ id })}`,
      {
        method: "GET",
        credentials: "include",
      },
    );

    return data;
  }

  static async updateEvent(payload: event.UpdateEventRequest) {
    const { data } = await this.fetchData<event.UpdateEventResponse>(`${this.BASE_URL}/event`, {
      method: "PUT",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    return data;
  }

  static validateDatesOnEvent({ startDate, endDate, eventDate, ...event }: Event) {
    if ([startDate, endDate, eventDate].includes(undefined)) return false;

    const eventDateTs = eventDate!.getTime();
    const startDateTs = startDate!.getTime();
    const endDateTs = endDate!.getTime();

    if (["IN-PERSON", "GAME"].includes(event.type)) {
      if (eventDateTs < startDateTs || eventDateTs < endDateTs) return false;
      if (startDateTs > endDateTs) return false;
    }

    return true;
  }

  static async invalidateCache() {
    await this.fetchData(`${this.BASE_URL}/event/invalidate-cache`, {
      method: "POST",
    });
  }
}
