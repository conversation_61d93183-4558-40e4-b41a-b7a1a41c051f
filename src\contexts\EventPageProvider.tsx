"use client";

import { useSession } from "@app/hooks/useSession";
import { EventService } from "@app/services/eventService";
import { useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { common, event } from "@mainframe-peru/types";
import React, { PropsWithChildren } from "react";

export type EventProviderProps = PropsWithChildren;

export type EventsPageFilters = {
  status: common.EventStatus | undefined;
  type: common.EventType | undefined;
};

type EventProviderValues = {
  filters: EventsPageFilters;
  events: event.CreateEventResponse[];
  isLoading: boolean;
  onSetEventType: (type: common.EventType | undefined) => void;
  onSetEventStatus: (status: common.EventStatus | undefined) => void;
  onSetEvents: (events: event.ListEventsResponse) => void;
  onSetIsLoading: (isLoading: boolean) => void;
};

export const EventsContext = React.createContext({} as EventProviderValues);

export function useEventsPage() {
  return React.useContext(EventsContext);
}

export function EventPageProvider({ children }: EventProviderProps) {
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [filters, setFilters] = React.useState<EventsPageFilters>({
    status: undefined,
    type: undefined,
  });
  const [events, setEvents] = React.useState<event.ListEventsResponse>([]);
  const toast = useToast();

  const onSetEventType = (type: common.EventType | undefined) => setFilters({ ...filters, type });
  const onSetEventStatus = (status: common.EventStatus | undefined) => setFilters({ ...filters, status });
  const onSetEvents = (events: event.ListEventsResponse) => setEvents(events);
  const onSetIsLoading = (isLoading: boolean) => setIsLoading(isLoading);

  const session = useSession();

  React.useEffect(() => {
    const fetchEvents = async () => {
      try {
        setIsLoading(true);
        const events = await EventService.getEvents({ ...filters, influencerId: session.admin?.influencerId });
        setEvents(events);
      } catch (error) {
        if (error instanceof AppError) {
          setEvents([]);
          toast({
            status: "error",
            title: "Error al mostrar los eventos",
            description: "No se pudo mostrar los eventos, contacte a soporte",
          });
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchEvents();
  }, [filters, toast, session.admin?.influencerId]);

  return (
    <EventsContext.Provider
      value={{ events, isLoading, filters, onSetEventType, onSetEventStatus, onSetEvents, onSetIsLoading }}
    >
      {children}
    </EventsContext.Provider>
  );
}
