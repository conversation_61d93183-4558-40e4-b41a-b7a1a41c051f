"use client";

import React from "react";
import { transactionsChartReducer } from "@app/stores/Reports/TransactionsChart/reducers";
import { useSession } from "@app/hooks/useSession";
import { TransactionsChartActions, TransactionsChartStore } from "@app/stores/Reports/TransactionsChart/types";
import { TRANSACTIONS_CHART_ACTION } from "@app/stores/Reports/TransactionsChart/constants";
import { TRANSACTIONS_CHART_STORE } from "@app/stores/Reports/TransactionsChart/store";
import { ReportsService } from "@app/services/reportService";
import { AppError } from "@mainframe-peru/common-core";
import { useToast } from "@chakra-ui/react";

export type TransactionsChartDispatchValue = {
  transactionsChartDispatcher: React.Dispatch<TransactionsChartActions>;
};

export const TransactionsChartContext = React.createContext({} as TransactionsChartStore);
export const TransactionsChartDispatch = React.createContext({} as React.Dispatch<TransactionsChartActions>);

export function TransactionsChartProvider({ children }: React.PropsWithChildren) {
  const [transactionsChartStore, transactionsChartDispatch] = React.useReducer(
    transactionsChartReducer,
    TRANSACTIONS_CHART_STORE,
  );

  const session = useSession();
  const toast = useToast();

  const fetchTransactionsChart = React.useCallback(async () => {
    try {
      const transactionsData = await ReportsService.getTransactionsCountByStateReport(session.admin!.influencerId);
      transactionsChartDispatch({
        type: TRANSACTIONS_CHART_ACTION.LOAD_TRANSACTIONS,
        payload: {
          isLoading: false,
          transactions: transactionsData,
        },
      });
    } catch (error) {
      transactionsChartDispatch({
        type: TRANSACTIONS_CHART_ACTION.LOAD_TRANSACTIONS,
        payload: {
          isLoading: false,
        },
      });
      if (error instanceof AppError) {
        toast({
          status: "error",
          title: error.name,
          description: error.message,
        });
      }
    }
  }, [toast, session.admin]);

  React.useEffect(() => {
    fetchTransactionsChart();
  }, [fetchTransactionsChart]);

  return (
    <TransactionsChartContext.Provider value={transactionsChartStore}>
      <TransactionsChartDispatch.Provider value={transactionsChartDispatch}>
        {children}
      </TransactionsChartDispatch.Provider>
    </TransactionsChartContext.Provider>
  );
}
