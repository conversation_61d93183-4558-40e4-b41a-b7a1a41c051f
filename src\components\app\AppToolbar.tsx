import { Box, Link, LinkProps } from "@chakra-ui/react";
import { AppDropdownMenu } from "@components/app/AppDropdownMenu";
import { SupportIcon } from "@public/svgs";

const buttonProps: LinkProps = {
  px: 13,
  py: 3,
  borderRadius: 13,
  bg: "black",
  color: "white",
  display: { md: "flex", base: "none" },
  fontSize: 13,
  justifyContent: "center",
  alignItems: "center",
  _hover: {
    bg: "black",
  },
  gap: 3,
};

function AppToolbar() {
  return (
    <Box display={"flex"} width={"90%"} gap={"12px"} justifyContent={"flex-end"}>
      <Link href="mailto:<EMAIL>" {...buttonProps}>
        <SupportIcon />
        Soporte
      </Link>
      <AppDropdownMenu />
    </Box>
  );
}
export { AppToolbar };
