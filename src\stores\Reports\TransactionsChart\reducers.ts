import {
  TransactionsChartActions,
  TransactionsChartChangeDateAction,
  TransactionsChartLoadAction,
  TransactionsChartStore,
} from "@app/stores/Reports/TransactionsChart/types";
import { TRANSACTIONS_CHART_ACTION } from "./constants";
import { changeDate, loadTransactionsReport } from "./actions";
import { ActionsDicionary } from "@app/stores/types";

export function transactionsChartReducer(state: TransactionsChartStore, action: TransactionsChartActions) {
  const transactionsActionsDic: ActionsDicionary<TransactionsChartStore> = {
    [TRANSACTIONS_CHART_ACTION.LOAD_TRANSACTIONS]: loadTransactionsReport(action as TransactionsChartLoadAction, state),
    [TRANSACTIONS_CHART_ACTION.CHANGE_DATE]: changeDate(action as TransactionsChartChangeDateAction, state),
  };

  return transactionsActionsDic[action.type] || state;
}
