import { Box, BoxProps } from "@chakra-ui/react";

export function KeyIcon({ width = "24px", height = "24px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none">
        <path
          d="M19.79 4.22007C16.83 1.27007 12.03 1.27007 9.09002 4.22007C7.02002 6.27007 6.40002 9.22007 7.20002 11.8201L2.50002 16.5201C2.17002 16.8601 1.94002 17.5301 2.01002 18.0101L2.31002 20.1901C2.42002 20.9101 3.09002 21.5901 3.81002 21.6901L5.99002 21.9901C6.47002 22.0601 7.14002 21.8401 7.48002 21.4901L8.30002 20.6701C8.50002 20.4801 8.50002 20.1601 8.30002 19.9601L6.36002 18.0201C6.07002 17.7301 6.07002 17.2501 6.36002 16.9601C6.65002 16.6701 7.13002 16.6701 7.42002 16.9601L9.37002 18.9101C9.56002 19.1001 9.88002 19.1001 10.07 18.9101L12.19 16.8001C14.78 17.6101 17.73 16.9801 19.79 14.9301C22.74 11.9801 22.74 7.17007 19.79 4.22007ZM14.5 12.0001C13.12 12.0001 12 10.8801 12 9.50007C12 8.12007 13.12 7.00007 14.5 7.00007C15.88 7.00007 17 8.12007 17 9.50007C17 10.8801 15.88 12.0001 14.5 12.0001Z"
          fill="currentColor"
        />
      </svg>
    </Box>
  );
}
