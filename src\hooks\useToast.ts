import { useToast as useChakraToast } from "@chakra-ui/react";
import React from "react";

export const useToast = () => {
  const toast = useChakraToast({
    isClosable: true,
    duration: 3000,
  });

  const toastRef = React.useRef(toast);
  const fire = React.useCallback(
    (title: string, description: string, status: "error" | "success" | "warning" | "info") =>
      toastRef.current({ title, description, status }),
    [],
  );

  return { fire, toastRef };
};
