export const TableTheming = {
  parts: ["table", "thead", "tbody", "tr", "th", "td"], // Define the parts of the Table
  baseStyle: {
    table: {
      borderCollapse: "separate",
      borderSpacing: "0 12px",
      width: "100%",
    },
    thead: {
      th: {
        color: "gray.50",
        fontWeight: "light",
        textAlign: "left",
        textTransform: "none",
        fontFamily: "inherit",
        fontSize: "15px",
      },
    },
    tbody: {
      td: {
        borderBottom: "0px",
        _first: {
          borderTopLeftRadius: "16px", // Apply to first cell in each row
          borderBottomLeftRadius: "16px",
        },
        _last: {
          borderTopRightRadius: "16px", // Apply to last cell in each row
          borderBottomRightRadius: "16px",
        },
        fontSize: "16px",
      },
      tr: {
        backgroundColor: "gray.400",
      },
    },
  },
};
