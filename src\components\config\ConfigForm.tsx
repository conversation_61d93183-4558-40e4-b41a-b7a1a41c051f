"use client";

import { useSession } from "@app/hooks/useSession";
import { Flex, Heading, Stack } from "@chakra-ui/react";
import { ImageDropzone } from "../common/ImageDropzone";
import React from "react";
import { Button } from "../common/Button";
import { InfluencerService } from "@app/services/influencerService";
import { StorageService } from "@app/services/storageService";

export function ConfigForm() {
  const { influencer } = useSession();

  const [imageUrl, setImageUrl] = React.useState((influencer.attributes?.banner as string) || "");
  const [saving, setSaving] = React.useState(false);
  const [bannerFile, setBannerFile] = React.useState<File>();

  async function onSaveClick() {
    if (!bannerFile) return;

    setSaving(true);
    const uploadResponse = await StorageService.upload({
      file: bannerFile,
      folderName: "business",
    });
    await InfluencerService.updateAttributes({
      attributes: {
        banner: uploadResponse.imageUrl,
      },
    });
    setSaving(false);
  }

  return (
    <Stack gap={5} bgColor="black" borderRadius={21} p={5}>
      <Flex alignItems={"center"} justify={"space-between"}>
        <Heading size={"md"} color={"blue.500"}>
          Configuración de {influencer.name}
        </Heading>
      </Flex>
      <Flex>Imagen de banner</Flex>
      <Flex>
        <ImageDropzone
          onDrop={(files) => {
            setBannerFile(files[0]);
            setImageUrl(URL.createObjectURL(files[0]));
          }}
          previewUrl={imageUrl}
        />
      </Flex>
      <Flex justifyContent="flex-end">
        <Button isLoading={saving} onClick={onSaveClick}>
          Guardar
        </Button>
      </Flex>
    </Stack>
  );
}
