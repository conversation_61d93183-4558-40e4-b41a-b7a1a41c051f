import { lottery } from "@app/components/common/Breadcrumb";
import { EventForm } from "@app/components/event/EventForm";
import { EventFormProvider } from "@app/components/event/EventForm/EventFormProvider";
import { ContainerLayout } from "@app/layouts/ContainerLayout";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Editar evento",
};
export default function EditEventPage() {
  return (
    <ContainerLayout>
      <PageLayout
        breadcrumb={{ parent: lottery, name: "Editar", url: "/premios-y-eventos/editar" }}
        title="Editar Evento"
      >
        <EventFormProvider>
          <EventForm isEditing />
        </EventFormProvider>
      </PageLayout>
    </ContainerLayout>
  );
}
