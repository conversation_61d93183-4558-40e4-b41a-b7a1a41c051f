import { BoxErrorIcon } from "@app/public/svgs/BoxErrorIcon";
import { Card, Text } from "@chakra-ui/react";
import React from "react";

function NoRecurrenceFound() {
  return (
    <Card
      height={{ base: 220, lg: 250 }}
      w="100%"
      maxW={450}
      bgColor="gray.400"
      m="auto"
      gap={3}
      display="flex"
      alignItems="center"
      justifyContent="center"
      borderRadius={21}
      color="white"
    >
      <BoxErrorIcon color={"white"} width={31} height={31} />
      <Text textAlign={"center"}>
        Este usuario no presenta una <br /> recurrencia disponible.
      </Text>
    </Card>
  );
}

export { NoRecurrenceFound };
