import { Flex, Spinner } from "@chakra-ui/react";
import React from "react";

export const Loader = () => {
  return (
    <Flex
      position="fixed"
      top={0}
      left={0}
      width="100vw"
      height="100vh"
      display="flex"
      justifyContent="center"
      alignItems="center"
      backgroundColor="black"
    >
      <Spinner />
    </Flex>
  );
};

export function useLoaderScreen() {
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const onLoad = (isLoading: boolean) => setIsLoading(isLoading);
  return {
    Loader,
    onLoad,
    isLoading,
  };
}
