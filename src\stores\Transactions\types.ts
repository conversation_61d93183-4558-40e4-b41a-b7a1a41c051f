import { TransactionState } from "@mainframe-peru/types/build/common";
import { ActionSchema } from "../types";
import { TRANSACTION_ACTIONS } from "./constants";
import { Transaction } from "@app/lib/transactions/types";

export type TransactionsPageStore = {
  transactions: Transaction[];
  transactionsFiltered: Transaction[];
  totalTransactions: {
    failed: number;
    success: number;
  };
  isLoading: boolean;
  filters: {
    limit: number;
    status?: TransactionState | undefined;
    date: {
      [k: string]: Date | undefined;
    };
  };
  currentTransaction: Transaction | undefined;
};

// ACTIONS FOR REDUCER

export type TransactionsLoadAction = ActionSchema<
  TRANSACTION_ACTIONS.LOAD_TRANSACTIONS,
  { isLoading: boolean; transactions?: Transaction[] }
>;

export type TransactionsChangeDateAction = ActionSchema<
  TRANSACTION_ACTIONS.CHANGE_DATE,
  { [k: string]: Date | undefined }
>;

export type TransactionsChangeStatusAction = ActionSchema<
  TRANSACTION_ACTIONS.FILTER_BY_STATUS,
  { status: TransactionState }
>;
export type TransactionsChangeLimitsRowsAction = ActionSchema<TRANSACTION_ACTIONS.LIMIT_ROWS, { limit: number }>;
export type SetCurrentTransactionAction = ActionSchema<
  TRANSACTION_ACTIONS.SET_CURRENT_TRANSACTION,
  { currentTransaction: Transaction }
>;

export type TransactionsActions =
  | TransactionsLoadAction
  | TransactionsChangeDateAction
  | TransactionsChangeStatusAction
  | TransactionsChangeLimitsRowsAction
  | SetCurrentTransactionAction;
