import { metricasTransacciones } from "@app/components/common/Breadcrumb";
import TransactionsChart from "@app/components/report/TransactionsChart";
import { TransactionsChartProvider } from "@app/contexts/Reports/TransactionsChart/provider";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Dashboard",
};

export default function TransactionPage() {
  return (
    <PageLayout breadcrumb={metricasTransacciones} title="Dashboard">
      <TransactionsChartProvider>
        <TransactionsChart />
      </TransactionsChartProvider>
    </PageLayout>
  );
}
