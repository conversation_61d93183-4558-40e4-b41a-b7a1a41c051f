import { BusinessForm } from "@app/components/business/BusinessForm";
import { BusinessFormProvider } from "@app/components/business/BusinessForm/BusinessFormProvider";
import { business } from "@app/components/common/Breadcrumb";
import { ContainerLayout } from "@app/layouts/ContainerLayout";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Crear negocio",
};

export default function CreateBusinessPage() {
  return (
    <ContainerLayout>
      <PageLayout
        breadcrumb={{ parent: business, name: "<PERSON>rea<PERSON>", url: "/promociones/negocios/crear" }}
        title="Crear negocio"
      >
        <BusinessFormProvider>
          <BusinessForm />
        </BusinessFormProvider>
      </PageLayout>
    </ContainerLayout>
  );
}
