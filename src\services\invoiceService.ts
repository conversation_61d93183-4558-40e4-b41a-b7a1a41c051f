import { BaseService } from "@app/lib/service/baseService";
import { invoice } from "@mainframe-peru/types";

export class InvoiceService extends BaseService {
  static async getInvoice(filter: invoice.http.GetInvoiceRequest): Promise<invoice.http.GetInvoiceResponse> {
    const url = `${this.BASE_URL}/invoice?${this.buildQueryParams(filter)}`;

    const { data } = await this.fetchData<invoice.http.GetInvoiceResponse>(url, {
      headers: {
        Accept: "application/json",
      },
    });
    return data;
  }
}
