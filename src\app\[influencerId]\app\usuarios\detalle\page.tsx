import { UserDetailProvider } from "@app/components/users/UserDetailProvider";
import { UserDetailSection } from "@app/components/users/UserDetailsSection";
import { UserTransactionsDetails } from "@app/components/users/UserTransactionsDetails";
import { ContainerLayout } from "@app/layouts/ContainerLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Detalle usuario",
};

export default async function SubscriptorPage() {
  return (
    <ContainerLayout>
      <UserDetailProvider>
        <UserDetailSection />
        <UserTransactionsDetails />
      </UserDetailProvider>
    </ContainerLayout>
  );
}
