import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { Transaction } from "@app/lib/transactions/types";
import { getCurrencyFormat } from "@app/lib/transactions/utils";
import { PaymentProviderEventService } from "@app/services/paymentProviderEventService";
import { Flex, Skeleton, Stack, StackDivider, Text } from "@chakra-ui/react";
import { invoice, ppe as ppeT } from "@mainframe-peru/types";
import { format } from "date-fns";
import React from "react";
import { Modal } from "../common/Modal";
import { TransactionStatusTag } from "./TransactionStatusTag";
import { InvoiceService } from "@app/services/invoiceService";
import { AppError } from "@mainframe-peru/common-core";

export function DetailModal({
  isOpen,
  onClose,
  transaction,
}: {
  isOpen: boolean;
  onClose: () => void;
  transaction: Transaction | undefined;
}) {
  const [ppe, setPPE] = React.useState<ppeT.GetPaymentProviderEventResponse>();
  const [invoice, setInvoice] = React.useState<invoice.http.GetInvoiceResponse | null | undefined>(undefined);
  const [loadingInvoice, setLoadingInvoice] = React.useState(false);

  const loadPpeEvent = async (id: number) => {
    const event = await PaymentProviderEventService.getOne({ transactionId: id });
    setPPE(event);
  };

  const loadInvoice = async (id: number) => {
    setLoadingInvoice(true);
    try {
      const res = await InvoiceService.getInvoice({ transactionId: id });
      setInvoice(res);
    } catch (e) {
      if (e instanceof AppError) {
        setInvoice(null);
      }
    } finally {
      setLoadingInvoice(false);
    }
  };

  React.useEffect(() => {
    setPPE(undefined);
    setInvoice(undefined);
    if (transaction) {
      loadPpeEvent(transaction.id);
      loadInvoice(transaction.id);
    }
  }, [transaction]);

  if (!transaction) return null;

  const createdAt = format(new Date(transaction.createdAt), TABLE_STANDARD_FORMAT_DATE);

  return (
    <Modal modalProps={{ isOpen, onClose }} bodyProps={{ p: 8, mt: 3 }}>
      <Stack divider={<StackDivider />} gap={2}>
        <Stack gap={3}>
          <Flex bgColor={"black"} pt={2} pb={2} pl={5} pr={5} borderRadius={16}>
            <Text mr={8}>{transaction.publicId}</Text>
            <TransactionStatusTag status={transaction.state} key={transaction.id} />
          </Flex>
          <Flex textColor={"gray.600"}>
            <Text>{createdAt}</Text>
          </Flex>
        </Stack>

        {/* Datos del usuario */}
        <Stack>
          <Text fontWeight={"bold"}>
            {transaction.user?.firstName} {transaction.user?.lastName}
          </Text>
          <Flex direction={"row"}>
            <Flex direction={"column"} flexGrow={1} textColor={"gray.600"} fontWeight={"bold"}>
              <Text>Tel:</Text>
              <Text>Email:</Text>
              <Text>País:</Text>
              <Text>Ciudad:</Text>
              <Text>Distrito:</Text>
              <Text>Dirección:</Text>
            </Flex>
            <Flex direction={"column"} flexGrow={1} textColor={"smokeWhite"}>
              <Text>{transaction.user?.phone}</Text>
              <Text>{transaction.user?.email}</Text>
              <Text>{transaction.user?.country}</Text>
              <Text>{transaction.user?.city}</Text>
              <Text>{transaction.user?.district || "-"}</Text>
              <Text>{transaction.user?.line1 || ""}</Text>
            </Flex>
          </Flex>
        </Stack>

        {/* Detalle de transacción */}
        <Stack>
          <Text fontWeight={"bold"}>Detalle</Text>
          <Flex direction={"row"}>
            <Flex direction={"column"} flexGrow={1} textColor={"gray.600"} fontWeight={"bold"}>
              <Text>Monto:</Text>
              <Text>Estado:</Text>
              <Text>Proveedor:</Text>
              <Text>Mensaje:</Text>
            </Flex>
            <Flex direction={"column"} flexGrow={1} textColor={"smokeWhite"}>
              <Text>{getCurrencyFormat(transaction.currency, transaction.amount)}</Text>
              <Text>{ppe?.state}</Text>
              <Text>{ppe?.provider}</Text>
              <Text>{ppe?.message}</Text>
            </Flex>
          </Flex>
        </Stack>

        {/* Factura */}
        <Stack>
          <Text fontWeight={"bold"}>Boleta/Factura</Text>
          {loadingInvoice && (
            <Stack>
              <Skeleton h="20px" w="60%" />
              <Skeleton h="20px" w="40%" />
            </Stack>
          )}

          {!loadingInvoice && !invoice && (
            <Text textColor="gray.600">No se encontró la boleta/factura para esta transacción.</Text>
          )}

          {!loadingInvoice && invoice && (
            <Flex direction="row">
              <Flex direction="column" flexGrow={1} textColor="gray.600" fontWeight="bold">
                <Text>ID:</Text>
                <Text>Link:</Text>
              </Flex>
              <Flex direction="column" flexGrow={1} textColor="smokeWhite" gap={1}>
                <Text>{invoice.id}</Text>
                <Text>
                  <a href={invoice.link} target="_blank" rel="noopener noreferrer">
                    Descargar
                  </a>
                </Text>
              </Flex>
            </Flex>
          )}
        </Stack>
      </Stack>
    </Modal>
  );
}
