"use client";

import { TransactionService } from "@app/services/transactionService";
import { ChevronDownIcon, DownloadIcon } from "@chakra-ui/icons";
import {
  Box,
  Button as CButton,
  Menu,
  MenuButton,
  MenuItemOption,
  MenuList,
  MenuOptionGroup,
  Stack,
  useToast,
} from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { common } from "@mainframe-peru/types";
import React from "react";
import { Button } from "../common/Button";
import { Datepicker } from "../common/Datepicker";
import { Modal } from "../common/Modal";

export function DownloadTransactionList() {
  const [transactionStatus, setTransactionStatus] = React.useState<common.TransactionState>();
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [isOpen, setIsOpen] = React.useState<boolean>(false);
  const [dateStart, setDateStart] = React.useState<Date | undefined>(() => {
    const today = new Date();
    today.setFullYear(today.getFullYear() - 1);
    return today;
  });
  const [dateEnd, setDateEnd] = React.useState<Date | undefined>(new Date());
  const toast = useToast();

  const handleDownloadList = async () => {
    setIsLoading(true);
    try {
      await TransactionService.getTransactionsCsv({
        detailed: true,
        limit: -1,
        state: transactionStatus,
        startDate: dateStart,
        endDate: dateEnd,
      });
      toast({
        status: "success",
        title: "Download successfully",
        description: "Su descarga se ha completado.",
      });
    } catch (error) {
      if (error instanceof AppError) {
        const { code, message } = error;
        toast({
          status: "error",
          title: code,
          description: message,
        });
      }
    }
    setIsLoading(false);
  };

  const transactionStatusMap: Record<common.TransactionState, string> = {
    SUCCESS: "Exitoso",
    FAIL: "Fallido",
    PROCESSING: "En proceso",
    REFUND: "Devuelto",
  };

  return (
    <>
      <Modal
        modalProps={{ isOpen, onClose: () => setIsOpen(false) }}
        title="Descarga de pagos"
        footerContent={
          <>
            <Button variant="ghost" onClick={() => setIsOpen(false)} mr={3}>
              Close
            </Button>
            <Button
              onClick={handleDownloadList}
              bgColor={"black"}
              color={"white"}
              _hover={{ color: "black", bgColor: "green.300" }}
            >
              Descargar
            </Button>
          </>
        }
      >
        <Stack gap={2} mb={3}>
          <Box>Status</Box>
          <Menu>
            <MenuButton as={CButton} rightIcon={<ChevronDownIcon />}>
              {transactionStatus ? transactionStatusMap[transactionStatus] : "Todos"}
            </MenuButton>
            <MenuList>
              <MenuOptionGroup
                type="radio"
                defaultValue={transactionStatus || "ALL"}
                value={transactionStatus || "ALL"}
                onChange={(d) => setTransactionStatus(d === "ALL" ? undefined : (d as "FAIL"))}
              >
                <MenuItemOption value="ALL">Todos</MenuItemOption>
                <MenuItemOption value="PROCESSING">En proceso</MenuItemOption>
                <MenuItemOption value="FAIL">Fallido</MenuItemOption>
                <MenuItemOption value="SUCCESS">Exitoso</MenuItemOption>
              </MenuOptionGroup>
            </MenuList>
          </Menu>
        </Stack>
        <Stack mb={3}>
          <Box>Pagos creados desde</Box>
          <Datepicker date={dateStart || new Date()} onChange={(d) => setDateStart(d)} />
        </Stack>
        <Stack>
          <Box>Pagos creados hasta</Box>
          <Datepicker date={dateEnd || new Date()} onChange={(d) => setDateEnd(d)} />
        </Stack>
      </Modal>
      <Button
        isLoading={isLoading}
        onClick={() => setIsOpen(true)}
        bgColor={"black"}
        color={"white"}
        _hover={{ color: "black", bgColor: "green.300" }}
        leftIcon={<DownloadIcon />}
        fontSize={13}
        minW={{ base: "100%", md: "fit-content" }}
      >
        Descargar pagos
      </Button>
    </>
  );
}
