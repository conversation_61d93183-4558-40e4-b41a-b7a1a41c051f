"use client";

import { But<PERSON> } from "@app/components/common/Button";
import { useSession } from "@app/hooks/useSession";
import { Flex } from "@chakra-ui/react";
import Link from "next/link";

export function BusinessToolbar() {
  const session = useSession();

  return (
    <Flex justifyContent="flex-end" mb={4}>
      <Link href={`/${session.admin?.influencerId}/app/promociones/negocios/crear`}>
        <Button fontSize={13} w={{ base: "100%", md: "auto" }}>
          Crear negocio
        </Button>
      </Link>
    </Flex>
  );
}
