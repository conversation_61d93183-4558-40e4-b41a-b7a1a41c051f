"use client";

import { BoxProps, Text, TextProps } from "@chakra-ui/react";
import { AppDrawer } from "@components/app/AppDrawer";
import { AppToolbar } from "@components/app/AppToolbar";
import { Header } from "@components/common/Header";
import { AppAside } from "./AppAside";
import { useSession } from "@app/hooks/useSession";

const headerDashboardProps: BoxProps = {
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  padding: {
    base: "15px 30px",
    lg: "15px 55px",
  },
  position: "absolute",
  top: 0,
  width: "100%",
  outline: "1px solid #404040",
  zIndex: 1000,
};

const titleProps: TextProps = {
  fontWeight: "bold",
  textTransform: "uppercase",
  fontSize: "16px",
};

const asideProps: BoxProps = {
  height: "100%",
  bg: "gray.400",
  display: "flex",
  padding: "15px 0",
  gap: "15px",
  flexDir: "column",
  alignItems: "center",
  justifyContent: "space-between",
};

export function AppHeader() {
  const session = useSession();
  return (
    <Header {...headerDashboardProps} minH={"70px"}>
      <Text {...titleProps} display={{ base: "none", xl: "block" }}>
        {session.admin?.influencerId}
      </Text>
      <AppDrawer>
        <AppAside {...asideProps} />
      </AppDrawer>
      <AppToolbar />
    </Header>
  );
}
