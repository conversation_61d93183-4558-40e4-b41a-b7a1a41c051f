import { Tag } from "../common/Tag";

export function TransactionStatusTag({ status }: { status: string }) {
  const state: { [k: string]: { colorScheme: string; text: string } } = {
    SUCCESS: {
      colorScheme: "green",
      text: "Exitoso",
    },
    FAIL: {
      colorScheme: "red",
      text: "Fallido",
    },
    PROCESSING: {
      colorScheme: "blue",
      text: "Procesando",
    },
  };
  return <Tag colorScheme={state[status || "FAIL"].colorScheme}>{state[status || "FAIL"].text}</Tag>;
}
