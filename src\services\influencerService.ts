import { BaseService } from "@app/lib/service/baseService";
import { influencer } from "@mainframe-peru/types";
import { PublicListInfluencerResponse } from "@mainframe-peru/types/build/influencer";

export class InfluencerService extends BaseService {
  static async getPublicInfluencer(influencerId: string): Promise<influencer.PublicGetInfluencerResponse | undefined> {
    const response = await fetch(`${this.BASE_URL}/influencer/public?id=${influencerId}`, {
      method: "GET",
    });

    return response.ok ? await response.json() : undefined;
  }

  static async getInfluencers(): Promise<PublicListInfluencerResponse> {
    const { data } = await this.fetchData<PublicListInfluencerResponse>(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/influencer/public-list-influencers`,
    );
    return data;
  }

  static async getInfluencer(influencerId: string): Promise<influencer.GetInfluencerResponse> {
    const response = await fetch(`${this.BASE_URL}/influencer?id=${influencerId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      credentials: "include",
    });

    return await response.json();
  }

  static async updateAttributes(
    body: influencer.UpdateInfluencerAttributesRequest,
  ): Promise<influencer.UpdateInfluencerAttributesResponse> {
    const response = await this.fetchData<influencer.UpdateInfluencerAttributesResponse>(
      `${this.BASE_URL}/influencer/attributes`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      },
    );

    return response.data;
  }
}
