"use client";

import React from "react";

import { useSession } from "@app/hooks/useSession";
import { useUsersPage } from "@app/hooks/useUsersPage";
import { EyeIcon } from "@app/public/svgs/EyeIcon";
import { Box, ButtonProps } from "@chakra-ui/react";
import { ListUsersResponse } from "@mainframe-peru/types/build/user";
import { useRouter } from "next/navigation";
import { Button } from "../common/Button";
import { Table } from "../common/Table";
import { SubscriptionStatusTag } from "./UserStatusTag";

export const buttonProps: ButtonProps = {
  bgColor: "transparent",
  _hover: {
    bgColor: "transparent",
  },
};

export function UsersTable() {
  const headers = ["Id", "Nombre", "Email", "Teléfono", "N° Doc", "Status", ""];
  const router = useRouter();
  const session = useSession();
  const { store, context } = useUsersPage();
  const { offset, onIncrement, onDecrement, fetchUsers } = context;

  const onChangePage = (onChangeOffset: () => void) => {
    return () => {
      onChangeOffset();
      fetchUsers({ offset: offset.current });
    };
  };

  const makeRows = (users: ListUsersResponse) => {
    return users.map(({ email, firstName, lastName, id, activeRecurrence, documentType, documentValue, phone }) => {
      const status = activeRecurrence ? "ACTIVE" : "NOT_ACTIVE";
      return [
        id,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {`${firstName} ${lastName}`}
        </Box>,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={300}>
          {email}
        </Box>,
        phone,
        `${documentType || ""} ${documentValue || ""}`,
        <SubscriptionStatusTag key={id} status={status} />,
        <Button
          bgColor={"transparent"}
          _hover={{ bgColor: "transparent" }}
          key={id}
          onClick={() => router.push(`/${session.admin?.influencerId}/app/usuarios/detalle/?id=${id}`)}
        >
          <EyeIcon />
        </Button>,
      ];
    });
  };

  return (
    <Table
      offset={offset.current}
      showButtons
      onNextPage={onChangePage(onIncrement)}
      onPrevPage={onChangePage(onDecrement)}
      isLoading={store.isLoading}
      boxProps={{ mt: 34 }}
      title="Todos los Usuarios"
      headers={headers}
      rows={[...makeRows(store.users)]}
    />
  );
}
