import { LoginForm } from "@app/components/login/Form";
import { InfluencerService } from "@app/services/influencerService";
import { Box, BoxProps, Text, TextProps } from "@chakra-ui/react";
import { Header } from "@components/common/Header";
import { RobotIcon } from "@public/svgs/RobotIcon";
import React from "react";

const boxProps: BoxProps = {
  as: "main",
  maxWidth: "90%",
  width: "378px",
  padding: "48px 32px",
  display: "flex",
  flexDir: "column",
  justifyContent: "center",
  alignItems: "center",
  borderRadius: "32px",
  gap: "12px",
  bg: "gray.500",
};

const svgProps: BoxProps = {
  width: "180px",
  height: "180px",
  display: "flex",
  justifyContent: "center",
  bg: "gray.400",
  borderRadius: "9999px",
  padding: "20px",
  color: "green.300",
};

const textProps: TextProps = {
  fontSize: "20px",
  as: "h1",
  textAlign: "center",
};

const headerLoginProps: BoxProps = {
  textAlign: "center",
  fontWeight: "bold",
  textTransform: "uppercase",
  position: "absolute",
  top: 0,
  width: "100%",
};

export async function generateStaticParams() {
  const influencers = await InfluencerService.getInfluencers();
  return influencers.map((inf) => ({
    influencerId: inf.id,
  }));
}

export default function LoginPage({ params }: { params: { influencerId: string } }) {
  const { influencerId } = params;
  return (
    <React.Fragment>
      <Header {...headerLoginProps}>Mainframe.APP</Header>
      <Box {...boxProps}>
        <RobotIcon {...svgProps} />
        <Text {...textProps}>
          Bienvenido a{" "}
          <Text as={"span"} textTransform={"capitalize"} fontWeight={"bold"}>
            {influencerId}
          </Text>
        </Text>
        <LoginForm influencerId={influencerId} />
      </Box>
    </React.Fragment>
  );
}
