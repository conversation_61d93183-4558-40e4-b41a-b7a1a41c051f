import { ActionSchema } from "../../types";
import { TRANSACTIONS_CHART_ACTION } from "./constants";
import { report } from "@mainframe-peru/types";

export type TransactionsCountByStateReport = report.TransactionCountByStateResponse;

export type TransactionsChartStore = {
  transactions: TransactionsCountByStateReport;
  transactionsFiltered: TransactionsCountByStateReport;
  isLoading: boolean;
  filters: {
    date: {
      [k: string]: Date | undefined;
    };
  };
};

// ACTIONS FOR REDUCER
export type TransactionsChartActionSchema<U extends number, T> = ActionSchema<U, T>;

export type TransactionsChartLoadAction = TransactionsChartActionSchema<
  TRANSACTIONS_CHART_ACTION.LOAD_TRANSACTIONS,
  { isLoading: boolean; transactions?: TransactionsCountByStateReport }
>;

export type TransactionsChartChangeDateAction = TransactionsChartActionSchema<
  TRANSACTIONS_CHART_ACTION.CHANGE_DATE,
  { [k: string]: Date | undefined }
>;

export type TransactionsChartActions = TransactionsChartLoadAction | TransactionsChartChangeDateAction;
