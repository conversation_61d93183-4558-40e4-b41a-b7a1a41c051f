import { ActionsDicionary } from "../types";
import { loadUsers } from "./actions";
import { USERS_PAGE_ACTION } from "./constants";
import { UsersLoadAction, UsersPageActions, UsersPageStore } from "./types";

export function influencerUsersReducer(state: UsersPageStore, action: UsersPageActions) {
  const usersPageActionDicionary: ActionsDicionary<UsersPageStore> = {
    [USERS_PAGE_ACTION.LOAD_USERS]: loadUsers(action as UsersLoadAction, state),
  };
  return usersPageActionDicionary[action.type] || state;
}
