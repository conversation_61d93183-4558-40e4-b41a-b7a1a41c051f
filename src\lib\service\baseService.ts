import { AppError } from "@mainframe-peru/common-core";
import { sessionExpired } from "../auth/utils";

export const getInfluencerIdFromURL = () => {
  const href = window.location.href;
  const url = new URL(href);
  return url.pathname.split("/").filter(Boolean)[0];
};

export type FetchResponse<T> = {
  data: T;
  headers?: Headers;
};

export abstract class BaseService {
  protected static readonly BASE_URL = "/api";

  /**
   * Método para construir query params a partir de un objeto
   */
  protected static buildQueryParams(params: Record<string, unknown>): string {
    const queryParams = new URLSearchParams();

    for (const [key, value] of Object.entries(params)) {
      if (value === undefined || value === null) continue;
      if (value instanceof Date) {
        queryParams.append(key, value.toISOString());
      } else if (typeof value === "object" && value !== null) {
        queryParams.append(key, JSON.stringify(value));
      } else {
        queryParams.append(key, String(value));
      }
    }

    return queryParams.toString();
  }

  /**
   * Método genérico para realizar fetch requests y manejar errores.
   * Puede retornar cualquier tipo de dato, según la API.
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  protected static async fetchData<T = unknown>(
    url: string,
    options: RequestInit = { method: "GET" },
  ): Promise<FetchResponse<T>> {
    if (sessionExpired() && typeof window !== "undefined") {
      window.location.href = `/${getInfluencerIdFromURL()}/login`;
      return {
        data: null as T,
      };
    }

    const response = await fetch(url, options);
    if (!response.ok) {
      const { code, message } = await response.json();
      throw new AppError({ code, message });
    }

    if (response.status === 204 || response.status === 304) {
      return {
        data: null as T,
        headers: response.headers,
      };
    }

    if (response.headers.get("Content-Type") === "text/csv") {
      return {
        data: (await response.text()) as T,
        headers: response.headers,
      };
    }

    return {
      data: await response.json(),
      headers: response.headers,
    };
  }

  static downloadCsv(content: string, contentDisposition: string | null) {
    const blob = new Blob([content], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${crypto.randomUUID()}.csv`;
    if (contentDisposition !== null) {
      const nameRegex = /attachment; filename="(.+)"/;
      const execution = nameRegex.exec(contentDisposition);
      if (execution) {
        const [, filename] = execution;
        a.download = filename;
      }
    }
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
}
