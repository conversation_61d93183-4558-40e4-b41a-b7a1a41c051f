"use client";
import { useComplaintsPage } from "@app/contexts/ComplaintsPageProvider";
import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { EyeIcon } from "@app/public/svgs/EyeIcon";
import { useDisclosure } from "@chakra-ui/react";
import { complaint as comp } from "@mainframe-peru/types";
import { format } from "date-fns";
import React from "react";
import { Button } from "../common/Button";
import { Table } from "../common/Table";
import { ComplaintDetailModal } from "./ComplaintDetailModal";
import { ComplaintStatusTag } from "./ComplaintStatusTag";
import { IssueTypeTag } from "./IssueTypeTag";

export function ComplaintsTable() {
  const { complaints, isLoading, onSetCurrentComplaint } = useComplaintsPage();
  const { isOpen, onClose, onOpen } = useDisclosure();

  const onModalOpen = (complaint: comp.Complaint) => {
    onSetCurrentComplaint(complaint);
    onOpen();
  };

  const makeRows = (complaints: comp.Complaint[]) => {
    return complaints.map((complaint) => {
      const { id, email, firstName, lastName, subject, type, status } = complaint;
      const createdAt = format(new Date(complaint.createdAt!), TABLE_STANDARD_FORMAT_DATE);
      const fullName = `${firstName} ${lastName}`;

      return [
        id,
        fullName,
        email,
        subject,
        <IssueTypeTag type={type} key={id} />,
        <ComplaintStatusTag key={id} status={status || "CLOSED"} />,
        createdAt,
        <ViewButton key={id} onOpen={() => onModalOpen(complaint)} />,
      ];
    });
  };
  return (
    <React.Fragment>
      <Table
        boxProps={{ mt: 34 }}
        title="Inbox"
        headers={["Id", "Nombre", "Email", "Subject", "Tipo", "Estado", "Fecha", ""]}
        rows={[...makeRows(complaints)]}
        isLoading={isLoading}
      />
      <ComplaintDetailModal isOpen={isOpen} onClose={onClose} />
    </React.Fragment>
  );
}

function ViewButton({ onOpen }: { onOpen: () => void }) {
  return (
    <Button onClick={onOpen} bgColor={"transparent"} _hover={{ bgColor: "transparent" }}>
      <EyeIcon />
    </Button>
  );
}
