"use client";

import {
  Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON>kraDrawer<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>kraDrawerOver<PERSON>,
  DrawerProps as ChakraDrawerProps,
  DrawerCloseButton as ChakraDrawerCloseButton,
} from "@chakra-ui/react";
import React from "react";

type DrawerProps = {
  title?: string;
  drawerProps: Omit<ChakraDrawerProps, "children">;
  footerContent?: React.ReactNode;
  children: React.ReactNode;
};

export function Drawer({ title, footerContent, drawerProps, children }: DrawerProps) {
  return (
    <ChakraDrawer {...drawerProps}>
      <ChakraDrawerOverlay />
      <ChakraDrawerContent bg={"gray.400"}>
        <ChakraDrawerCloseButton />
        {title && <ChakraDrawerHeader>{title}</ChakraDrawerHeader>}
        <ChakraDrawerBody height={"100%"}>{children}</ChakraDrawerBody>
        {footerContent && <ChakraDrawerFooter>{footerContent}</ChakraDrawerFooter>}
      </ChakraDrawerContent>
    </ChakraDrawer>
  );
}
