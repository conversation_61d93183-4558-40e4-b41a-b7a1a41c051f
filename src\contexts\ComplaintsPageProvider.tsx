"use client";

import { ComplaintService } from "@app/services/complaintService";
import { useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { common, complaint as comp } from "@mainframe-peru/types";
import { SupportStatus } from "@mainframe-peru/types/build/common";
import React, { PropsWithChildren } from "react";

export type ComplaintsPage = {
  complaints: comp.Complaint[];
  isLoading: boolean;
  currentComplaint: comp.Complaint;
  filters: ComplaintsPageFilters;
  onSetStatus: (status: SupportStatus | undefined) => void;
  onSetType: (type: common.ComplaintType | undefined) => void;
  onSetCurrentComplaint: (complaint: comp.Complaint) => void;
  onSetComplaints: (complaints: comp.Complaint[]) => void;
};

export const ComplaintsPageContext = React.createContext({} as ComplaintsPage);

export function useComplaintsPage() {
  return React.useContext(ComplaintsPageContext);
}

export type ComplaintsPageFilters = {
  type: common.ComplaintType | undefined;
  status: common.SupportStatus | undefined;
};

export function ComplaintsPageProvider({ children }: PropsWithChildren) {
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [complaints, setComplaints] = React.useState<comp.Complaint[]>([]);
  const [filters, setFilters] = React.useState<ComplaintsPageFilters>({
    status: undefined,
    type: undefined,
  });

  const [currentComplaint, setCurrentComplaint] = React.useState<comp.Complaint>({} as comp.Complaint);

  const toast = useToast();

  const onSetCurrentComplaint = (complaint: comp.Complaint) => {
    setCurrentComplaint(complaint);
  };

  const onSetComplaints = (complaints: comp.Complaint[]) => {
    setComplaints(complaints);
  };

  const onSetStatus = (status: SupportStatus | undefined) => {
    setFilters((prev) => ({ ...prev, status }));
  };

  const onSetType = (type: common.ComplaintType | undefined) => {
    setFilters((prev) => ({ ...prev, type }));
  };

  React.useEffect(() => {
    const fetchComplaints = async () => {
      setIsLoading(true);
      try {
        const complaints = await ComplaintService.getIssues(filters);
        setComplaints(complaints);
      } catch (error) {
        if (error instanceof AppError) {
          setComplaints([]);
          toast({
            status: "error",
            title: error.name,
            description: error.message,
          });
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchComplaints();
  }, [toast, filters]);

  return (
    <ComplaintsPageContext.Provider
      value={{
        isLoading,
        complaints,
        currentComplaint,
        filters,
        onSetCurrentComplaint,
        onSetType,
        onSetStatus,
        onSetComplaints,
      }}
    >
      {children}
    </ComplaintsPageContext.Provider>
  );
}
