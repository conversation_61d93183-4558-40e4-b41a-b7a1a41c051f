import {
  Menu,
  MenuButton,
  MenuButtonProps,
  MenuList,
  MenuListProps,
  MenuProps,
  Stack,
  StackProps,
} from "@chakra-ui/react";
import React from "react";

type DropdownProps = {
  buttonContent: React.ReactNode;
  listProps?: MenuListProps;
  buttonProps?: MenuButtonProps;
  stackProps?: StackProps;
} & React.PropsWithChildren &
  MenuProps;

export function DropdownMenu({
  buttonContent,
  children,
  listProps,
  buttonProps,
  stackProps,
  ...menuProps
}: DropdownProps) {
  return (
    <Menu {...menuProps}>
      <MenuButton {...buttonProps} type="button">
        {buttonContent}
      </MenuButton>
      <MenuList
        bg={"black"}
        padding={"24px"}
        borderRadius={"24px"}
        outline="1px solid"
        outlineColor={"gray.300"}
        {...listProps}
      >
        <Stack gap={"14px"} {...stackProps}>
          {children}
        </Stack>
      </MenuList>
    </Menu>
  );
}
