import { formatDate } from "@app/lib/transactions/utils";
import { TransactionService } from "@app/services/transactionService";
import { TRANSACTION_ACTIONS } from "@app/stores/Transactions/constants";
import { TransactionsActions, TransactionsPageStore } from "@app/stores/Transactions/types";
import { useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";

export const useTransactionsDatepickers = (
  store: TransactionsPageStore,
  dispatch: React.Dispatch<TransactionsActions>,
) => {
  const toast = useToast();

  const onChangeDate = (date: Date, target: string) => {
    dispatch({
      type: TRANSACTION_ACTIONS.CHANGE_DATE,
      payload: {
        [target]: date,
      },
    });
  };

  const onClearDate = (target: string) => {
    dispatch({
      type: TRANSACTION_ACTIONS.CHANGE_DATE,
      payload: {
        [target]: undefined,
      },
    });

    onApplyDate({ ...store.filters.date, [target]: undefined });
  };

  const onApplyDate = async (payload: { from?: Date | undefined; to?: Date | undefined }) => {
    try {
      dispatch({
        type: TRANSACTION_ACTIONS.LOAD_TRANSACTIONS,
        payload: {
          isLoading: true,
        },
      });

      const transactions = await TransactionService.getTransactions({
        startDate: payload.from ? formatDate(payload.from) : undefined,
        endDate: payload.to ? formatDate(payload.to) : undefined,
      });

      dispatch({
        type: TRANSACTION_ACTIONS.LOAD_TRANSACTIONS,
        payload: {
          isLoading: false,
          transactions,
        },
      });
    } catch (error) {
      if (error instanceof AppError) {
        toast({ status: "error", title: error.name, description: error.message });
      }
    }
  };

  return {
    onChangeDate,
    onClearDate,
    onApplyDate,
  };
};
