import { BaseService } from "@app/lib/service/baseService";
import { complaint as comp } from "@mainframe-peru/types";

export class ComplaintService extends BaseService {
  static async getIssues(query?: comp.ListComplaintsRequest): Promise<comp.Complaint[]> {
    const url = `${this.BASE_URL}/complaint/list-issues?${this.buildQueryParams(query || {})}`;
    const response = await this.fetchData<comp.ListComplaintsResponse>(url, { credentials: "include" });
    return response.data.complaints;
  }

  static async updateIssue(payload: comp.UpdateComplaintRequest): Promise<comp.UpdateComplaintResponse> {
    const response = await this.fetchData<comp.UpdateComplaintResponse>(`${this.BASE_URL}/complaint/`, {
      credentials: "include",
      body: JSON.stringify(payload),
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data;
  }
}
