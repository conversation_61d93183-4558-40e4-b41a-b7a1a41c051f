"use client";

import { useSession } from "@app/hooks/useSession";
import { CodeService } from "@app/services/codeService";
import { useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { businessPromotionCode } from "@mainframe-peru/types";
import React, { PropsWithChildren } from "react";

export type CodeProviderProps = PropsWithChildren;

export type CodePageFilters = {
  promotionId?: number;
};

type CodeProviderValues = {
  codes: businessPromotionCode.ListBusinessPromotionCodeResponse;
  isLoading: boolean;
};

export const CodeContext = React.createContext({} as CodeProviderValues);

export function useCodePage() {
  return React.useContext(CodeContext);
}

export function CodePageProvider({ children, promotionId }: CodeProviderProps & { promotionId?: number }) {
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [codes, setCodes] = React.useState<businessPromotionCode.ListBusinessPromotionCodeResponse>([]);
  const toast = useToast();
  const session = useSession();

  React.useEffect(() => {
    const fetchCodes = async () => {
      setIsLoading(true);
      try {
        const codes = await CodeService.listCodes({ promotionId });
        setCodes(codes);
      } catch (error) {
        if (error instanceof AppError) {
          setCodes([]);
          toast({
            status: "error",
            title: "Error al mostrar los códigos",
            description: "No se pudo mostrar los códigos, contacte a soporte",
          });
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchCodes();
  }, [promotionId, toast, session.admin?.influencerId]);

  return <CodeContext.Provider value={{ codes, isLoading }}>{children}</CodeContext.Provider>;
}
