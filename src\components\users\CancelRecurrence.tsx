"use client";

import { CardIcon } from "@app/public/svgs";
import { Stack, useDisclosure } from "@chakra-ui/react";
import React from "react";
import { Button } from "../common/Button";
import { CancelRecurrenceModal } from "./CancelRecurrenceModal";
import { useSession } from "@app/hooks/useSession";

export function CancelRecurrence() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const session = useSession();
  return (
    <React.Fragment>
      <Stack spacing={5} maxW={450} marginTop={31} mx={"auto"}>
        {session.isInPolicy("recurrence", "DELETE_RECURRENCE") && (
          <Button
            onClick={() => onOpen()}
            leftIcon={<CardIcon />}
            borderColor={"#595959"}
            bgColor={"transparent"}
            variant={"outline"}
            w={"100%"}
            _hover={{
              bgColor: "red.400",
              borderColor: "red.400",
            }}
          >
            Cancelar suscripción
          </Button>
        )}
      </Stack>
      <CancelRecurrenceModal isOpen={isOpen} onOpen={onOpen} onClose={onClose} />
    </React.Fragment>
  );
}
