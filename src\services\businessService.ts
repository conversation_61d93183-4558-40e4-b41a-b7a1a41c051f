import { BaseService } from "@app/lib/service/baseService";
import { business } from "@mainframe-peru/types";

export class BusinessService extends BaseService {
  static createFormData(payload: business.CreateBusinessRequest & { file?: File; id?: string }): FormData {
    const formData = new FormData();

    for (const key in payload) {
      const value = payload[key as keyof business.CreateBusinessRequest];

      if (!value) continue;

      if (key !== "file") {
        formData.append(key, String(value));
        continue;
      }

      formData.append(key, payload[key]!);
    }
    return formData;
  }

  static async getBusinesses(query: Partial<business.ListBusinessRequest>): Promise<business.ListBusinessResponse> {
    const url = `${this.BASE_URL}/business/list?${this.buildQueryParams(query)}`;
    const { data } = await this.fetchData<business.ListBusinessResponse>(url, { credentials: "include" });
    return data;
  }

  static async createBusiness(payload: FormData): Promise<business.CreateBusinessRequest> {
    const { data } = await this.fetchData<business.CreateBusinessResponse>(`${this.BASE_URL}/business/`, {
      method: "POST",
      credentials: "include",
      body: payload,
    });

    return data;
  }

  static async getBusiness(id: string) {
    const { data } = await this.fetchData<business.GetBusinessResponse>(
      `${this.BASE_URL}/business?${this.buildQueryParams({ id })}`,
      {
        method: "GET",
        credentials: "include",
      },
    );

    return data;
  }

  static async updateBusiness(payload: FormData) {
    const { data } = await this.fetchData<business.UpdateBusinessResponse>(`${this.BASE_URL}/business`, {
      method: "PUT",
      credentials: "include",
      body: payload,
    });
    return data;
  }
}
