"use client";

import { useTransactionsPage } from "@app/hooks/useTransactionsPage";
import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { Transaction } from "@app/lib/transactions/types";
import { getCurrencyFormat } from "@app/lib/transactions/utils";
import { EyeIcon } from "@app/public/svgs/EyeIcon";
import { TRANSACTION_ACTIONS } from "@app/stores/Transactions/constants";
import { useDisclosure } from "@chakra-ui/react";
import { format } from "date-fns";
import React from "react";
import { Button } from "../common/Button";
import { Table } from "../common/Table";
import { DetailModal } from "./TransactionDetailModal";
import { TransactionStatusTag } from "./TransactionStatusTag";

export function TransactionsTable() {
  const { store, dispatch } = useTransactionsPage();
  const { isOpen, onClose, onOpen } = useDisclosure();

  const onModalOpen = (transaction: Transaction) => {
    dispatch({
      type: TRANSACTION_ACTIONS.SET_CURRENT_TRANSACTION,
      payload: {
        currentTransaction: transaction,
      },
    });
    onOpen();
  };

  const makeRows = (transactions: Transaction[]) => {
    return transactions.map((transaction) => {
      const createdAt = format(new Date(transaction.createdAt), TABLE_STANDARD_FORMAT_DATE);
      const amountFormatted = getCurrencyFormat(transaction.currency, transaction.amount);

      return [
        transaction.id,
        createdAt,
        `${transaction.user?.firstName} ${transaction.user?.lastName}`,
        <TransactionStatusTag status={transaction.state} key={transaction.id} />,
        amountFormatted,
        <ViewButton key={transaction.id} onOpen={() => onModalOpen(transaction)} />,
      ];
    });
  };

  // const transactions = store.filters.limit ? store.transactionsFiltered : store.transactions;
  const status = store.filters.status === "SUCCESS" ? "Exitosos" : store.filters.status === "FAIL" ? "Fallidos" : "";
  return (
    <React.Fragment>
      <Table
        boxProps={{
          mt: 8,
        }}
        title={`Últimos 100 Pagos ${status}`}
        isLoading={store.isLoading}
        headers={["ID", "Fecha", "Nombre", "Estado", "Monto", ""]}
        rows={[...makeRows(store.transactions)]}
      />
      <DetailModal isOpen={isOpen} onClose={onClose} transaction={store.currentTransaction} />
    </React.Fragment>
  );
}

function ViewButton({ onOpen }: { onOpen: () => void }) {
  return (
    <Button onClick={onOpen} bgColor={"transparent"} _hover={{ bgColor: "transparent" }}>
      <EyeIcon />
    </Button>
  );
}
