"use client";

import { useCodePage } from "@app/hooks/useCodePage";
import { businessPromotionCode } from "@mainframe-peru/types";
import { Table } from "../common/Table";
import { Box, useDisclosure } from "@chakra-ui/react";
import { CheckIcon, DeleteIcon } from "@chakra-ui/icons";
import { DeleteCodeModal } from "./DeleteCodeModal";
import { Button } from "../common/Button";
import React, { useState } from "react";

export function CodesTable() {
  const { codes, isLoading } = useCodePage();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [currentId, setCurrentId] = useState<number>(0);

  const makeRows = (codes: businessPromotionCode.ListBusinessPromotionCodeResponse) => {
    return codes.map((promotionCode) => {
      const { code, id, userId } = promotionCode;

      return [
        id,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {code}
        </Box>,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {userId ? <CheckIcon /> : ""}
        </Box>,
        <DeleteRow
          key={id}
          onOpen={() => {
            setCurrentId(id);
            onOpen();
          }}
        />,
      ];
    });
  };

  return (
    <React.Fragment>
      <Table
        isLoading={isLoading}
        rows={makeRows(codes)}
        boxProps={{ mt: 34 }}
        headers={["Id", "Código", "Reclamado", ""]}
      />
      <DeleteCodeModal id={currentId} isOpen={isOpen} onOpen={onOpen} onClose={onClose} />
    </React.Fragment>
  );
}

function DeleteRow({ onOpen }: { onOpen: () => void }) {
  return (
    <Button onClick={onOpen} bgColor={"transparent"} _hover={{ bgColor: "transparent" }}>
      <DeleteIcon color={"white"} />
    </Button>
  );
}
