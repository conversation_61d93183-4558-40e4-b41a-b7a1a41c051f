import { pagos } from "@app/components/common/Breadcrumb";
import { TransactionsTable } from "@app/components/transactions/TransactionsTable";
import { TransactionsToolbar } from "@app/components/transactions/TransactionsToolbar";
import { TransactionsPageProvider } from "@app/contexts/TransactionsPageProvider";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Pagos",
};

export default async function PaymentsPage() {
  return (
    <PageLayout breadcrumb={pagos} title="Pagos">
      <TransactionsPageProvider>
        <TransactionsToolbar />
        <TransactionsTable />
      </TransactionsPageProvider>
    </PageLayout>
  );
}
