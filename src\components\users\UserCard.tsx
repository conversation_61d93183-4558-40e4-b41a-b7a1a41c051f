"use client";

import { CARD_BRANDS } from "@app/lib/common/constants";
import { CardIcon } from "@app/public/svgs";
import { AmexIcon } from "@app/public/svgs/AmexIcon";
import { CardDotsIcon } from "@app/public/svgs/CardDotsIcon";
import { DinersIcon } from "@app/public/svgs/DinersIcon";
import { MasterCardIcon } from "@app/public/svgs/MasterCardIcon";
import { VisaIcon } from "@app/public/svgs/VisaIcon";
import { YapeIcon } from "@app/public/svgs/YapeIcon";
import { Box, BoxProps, Card, Stack, Text } from "@chakra-ui/react";

type DefaultCardProps = {
  name: string;
  number?: string;
  amount: string;
  brand: keyof typeof CARD_BRANDS;
};

type CardColors = {
  bgColor: string;
  color: string;
};

// Diccionario para colores y fondos de las tarjetas
const COLORS_DICTIONARY: Record<string, CardColors> = {
  [CARD_BRANDS.Yape]: {
    bgColor: "linear-gradient(270deg, #5F0672 0%, #8374F9 100%)",
    color: "purple.500",
  },
  [CARD_BRANDS.Amex]: {
    bgColor: "linear-gradient(90deg, #3385FF 0%, #0028E0 100%)",
    color: "blue.500",
  },
  [CARD_BRANDS.Visa]: {
    bgColor: "linear-gradient(90deg, #3385FF 0%, #0028E0 100%)",
    color: "blue.500",
  },
  [CARD_BRANDS.Diners]: {
    bgColor: "linear-gradient(90deg, #3385FF 0%, #0028E0 100%)",
    color: "blue.500",
  },
  [CARD_BRANDS.Mastercard]: {
    bgColor: "linear-gradient(90deg, #3385FF 0%, #0028E0 100%)",
    color: "blue.500",
  },
  [CARD_BRANDS.Default]: {
    bgColor: "linear-gradient(90deg, #00FF75 0%, #21b465 100%)",
    color: "black",
  },
};

// Componente de iconos según el proveedor de tarjeta
export function PaymentProviderIcon({ brand, ...rest }: { brand: keyof typeof CARD_BRANDS } & BoxProps) {
  const IconComponents = {
    [CARD_BRANDS.Visa]: VisaIcon,
    [CARD_BRANDS.Yape]: YapeIcon,
    [CARD_BRANDS.Mastercard]: MasterCardIcon,
    [CARD_BRANDS.Amex]: AmexIcon,
    [CARD_BRANDS.Diners]: DinersIcon,
    [CARD_BRANDS.Default]: CardIcon,
  };

  const IconComponent = IconComponents[brand] || IconComponents[CARD_BRANDS.Default];
  return <IconComponent {...rest} />;
}

export function UserCard({ name, number, amount, brand }: DefaultCardProps) {
  const { bgColor, color } = COLORS_DICTIONARY[brand] || COLORS_DICTIONARY[CARD_BRANDS.Default];
  return (
    <Card
      height={{ base: 220, lg: 250 }}
      w="100%"
      maxW={450}
      m="auto"
      position="relative"
      borderRadius={21}
      bgImage={bgColor}
    >
      <PaymentProviderIcon brand={brand} color="white" position="absolute" top={5} right={5} />
      <Box px={2} borderRadius={8} bg="white" w="fit-content" color={color} top={5} left={5} position="absolute">
        <Text fontWeight="bold">S/{Number(amount).toFixed(2)}</Text>
      </Box>
      <CardDotsIcon position="absolute" right={5} top="50%" />
      <Stack color="white" position="absolute" bottom={5} left={5} spacing={3}>
        <Text fontWeight="bold" fontSize={21} letterSpacing={1.5}>
          {number}
        </Text>
        <Text>{name}</Text>
      </Stack>
    </Card>
  );
}
