"use client";

import { useOverflow } from "@app/hooks/useOverflow";
import { LIST_MONTHS_NAMES, LIST_WEEK_DAYS, STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { DirectionIcon } from "@app/public/svgs/DirectionIcon";
import { Box, Button, Flex, Heading, Input, InputGroup, InputProps, Stack, useOutsideClick } from "@chakra-ui/react";
import { addMonths, format, getDate, isSameDay, isSameMonth, isToday, set, subMonths } from "date-fns";
import React from "react";
import { DatepickerProvider, useDatepicker } from "./provider";

export type DatepickerProps = {
  date?: Date;
  onChange?: (date: Date) => void;
  onReset?: () => void;
  type?: "date" | "datetime-local";
  styles?: InputProps & { readonly?: boolean; disabled?: boolean };
};

export const Datepicker = ({ type = "datetime-local", ...rest }: DatepickerProps) => {
  return (
    <DatepickerProvider {...rest} type={type}>
      <DatepickerTrigger {...rest.styles} />
      <DatepickerCalendar />
    </DatepickerProvider>
  );
};

export const DatepickerTrigger = ({ ...props }: InputProps) => {
  const { currentDate, type, onOpen, onChangeDate } = useDatepicker();
  return (
    <InputGroup w="auto" onClick={() => onOpen()}>
      <Input
        {...props}
        type={type}
        step={1}
        sx={{
          "&::-webkit-calendar-picker-indicator": {
            display: "none",
          },
          "&::-webkit-clear-button": {
            display: "none",
          },
          "&::-moz-calendar-picker-indicator": {
            display: "none",
          },
        }}
        value={currentDate ? format(currentDate, type === "date" ? "yyyy-MM-dd" : STANDARD_FORMAT_DATE) : ""}
        max={type === "date" ? "9999-12-31" : "9999-12-31T23:59"}
        onChange={(e) => {
          if (!e.target.value) return;
          const parsedDate = new Date(type === "date" ? `${e.target.value}T00:00:00` : e.target.value);
          onChangeDate(parsedDate);
        }}
        color="white"
        fontSize={13}
      />
    </InputGroup>
  );
};

export const DatepickerCalendar = () => {
  const { onClose, getDisclosureProps, isOpen } = useDatepicker();
  const ref = React.useRef<HTMLDivElement | null>(null);
  const elementOverflow = useOverflow(ref, isOpen);
  useOutsideClick({
    ref,
    handler: () => {
      if (isOpen) {
        onClose();
      }
    },
  });
  return (
    <Box
      ref={ref}
      borderRadius={13}
      border="1px solid #8b8b8bcc"
      minW={["100%", 300]}
      bgColor="black"
      position="absolute"
      inset={{ base: "unset", md: !elementOverflow ? "48px auto auto auto" : "48px 3px auto auto" }}
      zIndex={1}
      {...getDisclosureProps()}
    >
      <Stack gap={5} p={5}>
        <DatepickerCalendarHeader />
        <Calendar />
        <DatepickerCalendarFooter />
      </Stack>
    </Box>
  );
};

export const DatepickerCalendarHeader = () => {
  const { currentDate, onChangeDate } = useDatepicker();
  const date = currentDate ?? new Date();
  const currentMonth = LIST_MONTHS_NAMES[date?.getMonth()];
  const currentYear = currentDate?.getFullYear();
  return (
    <Flex align="center" justify="space-between">
      <Heading size="sm" textTransform="capitalize">
        {currentMonth} {currentYear}
      </Heading>
      <Flex gap={1}>
        <Button
          onClick={() => onChangeDate(addMonths(date, 1))}
          bgColor="black"
          _hover={{ bgColor: "black" }}
          color="white"
          p={0}
          w="fit-content"
        >
          <DirectionIcon />
        </Button>
        <Button
          onClick={() => onChangeDate(subMonths(date, 1))}
          bgColor="black"
          _hover={{ bgColor: "black" }}
          color="white"
          p={0}
          w="fit-content"
        >
          <DirectionIcon transform="rotateX(180deg)" />
        </Button>
      </Flex>
    </Flex>
  );
};

export const Calendar = () => {
  const { calendarDays, currentDate, onChangeDate, onClose } = useDatepicker();
  const onSelectDate = (date: Date) => {
    const updatedDate = set(date, {
      hours: currentDate?.getHours(),
      minutes: currentDate?.getMinutes(),
      seconds: currentDate?.getSeconds(),
    });

    onClose();
    onChangeDate(updatedDate);
  };

  return (
    <Stack fontSize={13} gap={5}>
      <Box display="grid" gridTemplateColumns="repeat(7, 1fr)">
        {LIST_WEEK_DAYS.map((day) => (
          <Box key={day} color="white" textTransform="capitalize" textAlign="center">
            {day}
          </Box>
        ))}
      </Box>
      <Box display="grid" gridTemplateColumns="repeat(7, 1fr)" rowGap={2} placeItems="center">
        {calendarDays.map((day) => (
          <Flex
            w={21}
            h={21}
            onClick={() => onSelectDate(day)}
            dateTime={format(day, STANDARD_FORMAT_DATE)}
            as="time"
            p={1}
            key={format(day, STANDARD_FORMAT_DATE)}
            align="center"
            justify="center"
            borderRadius={3}
            color={
              currentDate && isSameDay(day, currentDate)
                ? "black"
                : isSameMonth(day, currentDate || new Date()) && !isToday(day)
                  ? "white"
                  : isSameMonth(day, currentDate || new Date()) && isToday(day)
                    ? "black"
                    : "gray"
            }
            bgColor={isToday(day) ? "white" : isSameDay(day, currentDate || new Date()) ? "green.300" : "black"}
          >
            {getDate(day)}
          </Flex>
        ))}
      </Box>
    </Stack>
  );
};

export const DatepickerCalendarFooter = () => {
  const { onResetDate } = useDatepicker();
  return (
    <Flex justify="flex-end">
      <Button borderRadius={8} onClick={() => onResetDate()}>
        Limpiar
      </Button>
    </Flex>
  );
};
