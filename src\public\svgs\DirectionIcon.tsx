import { Box, BoxProps } from "@chakra-ui/react";

export function DirectionIcon({ width = "16px", height = "11px", ...props }: BoxProps) {
  return (
    <Box {...props} width={width} height={height}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 16 11" fill="none">
        <path d="M2 2L8 8L14 2" stroke="currentColor" strokeWidth="3" />
      </svg>
    </Box>
  );
}
