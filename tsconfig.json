{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@app/*": ["./src/*"], "@components/*": ["./src/components/*"], "@contexts/*": ["./src/contexts/*"], "@hooks/*": ["./src/hooks/*"], "@layouts/*": ["./src/layouts/*"], "@lib/*": ["./src/lib/*"], "@public/*": ["./src/public/*"], "@services/*": ["./src/services/*"], "@styles/*": ["./src/styles/*"], "@types/*": ["./src/lib/types/*"], "@utils/*": ["./src/utils/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}