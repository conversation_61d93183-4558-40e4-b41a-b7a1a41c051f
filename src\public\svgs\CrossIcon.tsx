import { Box, BoxProps } from "@chakra-ui/react";

export function CrossIcon({ width = "6px", height = "6px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 6 7" fill="none">
        <path
          d="M4.79985 0.500156L5.99981 1.70012L1.19997 6.49995L8.14064e-06 5.29999L4.79985 0.500156Z"
          fill="#0D0D0D"
        />
        <path d="M5.9998 5.29996L4.79984 6.49992L0 1.70008L1.19996 0.500122L5.9998 5.29996Z" fill="#0D0D0D" />
      </svg>
    </Box>
  );
}
