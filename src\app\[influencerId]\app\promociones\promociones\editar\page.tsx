import { promotions } from "@app/components/common/Breadcrumb";
import { PromotionForm } from "@app/components/promotion/PromotionForm";
import { PromotionFormProvider } from "@app/components/promotion/PromotionForm/PromotionFormProvider";
import { ContainerLayout } from "@app/layouts/ContainerLayout";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Editar promoción",
};
export default function EditPromotionPage() {
  return (
    <ContainerLayout>
      <PageLayout
        breadcrumb={{ parent: promotions, name: "Editar", url: "/promociones/promociones/editar" }}
        title="Editar Promoción"
      >
        <PromotionFormProvider>
          <PromotionForm isEditing />
        </PromotionFormProvider>
      </PageLayout>
    </ContainerLayout>
  );
}
