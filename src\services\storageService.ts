import { BaseService } from "@app/lib/service/baseService";
import { storage } from "@mainframe-peru/types";

type UploadInput = {
  file: File;
} & storage.UploadImageRequest;

export class StorageService extends BaseService {
  static async upload(input: UploadInput): Promise<storage.UploadImageResponse> {
    const formData = new FormData();
    formData.append("file", input.file);
    formData.append("folderName", input.folderName);
    if (input.id) formData.append("id", input.id);
    if (input.metadata) formData.append("id", input.metadata);

    const response = await this.fetchData<storage.UploadImageResponse>(`${this.BASE_URL}/storage`, {
      method: "POST",
      body: formData,
    });

    return response.data;
  }
}
