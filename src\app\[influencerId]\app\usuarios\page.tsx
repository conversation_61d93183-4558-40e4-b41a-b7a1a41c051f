import { suscriptores } from "@app/components/common/Breadcrumb";
import { DownloadUsersList } from "@app/components/users/DownloadUsersList";
import { UserSearch } from "@app/components/users/UserSearch";
import { UsersTable } from "@app/components/users/UsersTable";
import { UsersPageProvider } from "@app/contexts/UsersPageProvider";
import { PageLayout } from "@app/layouts/PageLayout";
import { Flex } from "@chakra-ui/react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Usuarios",
};

export default function SubscritorsPage() {
  return (
    <PageLayout breadcrumb={suscriptores} title="Suscriptores">
      <UsersPageProvider>
        <Flex align={"flex-start"} justify={"space-between"} gap={55} mb={34} flexDir={{ base: "column", md: "row" }}>
          <UserSearch />
          <DownloadUsersList />
        </Flex>
        <UsersTable />
      </UsersPageProvider>
    </PageLayout>
  );
}
