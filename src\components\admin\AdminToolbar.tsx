"use client";

import { Flex, useToast } from "@chakra-ui/react";
import { Search } from "../common/Search";
import { Button } from "../common/Button";
import { CardIconTransparent } from "@app/public/svgs/CardIconTransparent";
import { useAdminsPage } from "@app/contexts/AdminsPageProvider";
import React from "react";
import { AdminService } from "@app/services/adminService";
import { AppError } from "@mainframe-peru/common-core";
import Link from "next/link";
import { useSession } from "@app/hooks/useSession";

export function AdminToolbar() {
  const { onSetAdmin, onSetIsLoading } = useAdminsPage();
  const toast = useToast();
  const inputRef = React.useRef<HTMLInputElement | null>(null);
  const session = useSession();

  const onSearch = (value: string) => {
    onSetIsLoading(true);
    AdminService.getAdmins({
      influencerId: session.influencer.id,
      compound: value,
    })
      .then((admins) => {
        onSetAdmin(admins);
        onSetIsLoading(false);
      })
      .catch((error) => {
        onSetIsLoading(false);
        if (error instanceof AppError) {
          toast({
            status: "error",
            description: error.message,
            title: error.code,
          });
        }
      });
  };

  const onClick = () => {
    if (inputRef.current && inputRef.current.value.length === 0) {
      return;
    }
    onSearch(inputRef.current?.value || "");
  };

  return (
    <Flex gap={13} alignItems={"center"} flexDir={{ base: "column", md: "row" }} w={"100%"}>
      <Search
        ref={inputRef}
        onSearch={onSearch}
        flexProps={{ flexBasis: "100%", width: { base: "100%", md: "auto" } }}
        placeholder="Ingresa el correo, nombre o apellido..."
      />
      <Button
        w={{ base: "100%", md: "auto" }}
        color={"white"}
        fontSize={13}
        bg={"black"}
        _hover={{ bg: "black" }}
        onClick={onClick}
        leftIcon={<CardIconTransparent />}
      >
        Buscar
      </Button>
      {session.isInPolicy("admin", "PUT_ADMIN") && (
        <Link href={`/${session.admin?.influencerId}/app/admins/crear/`}>
          <Button fontSize={13} w={{ base: "100%", md: "auto" }}>
            Crear admin
          </Button>
        </Link>
      )}
    </Flex>
  );
}
