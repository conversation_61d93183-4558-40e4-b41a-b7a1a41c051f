"use client";
import { ChevronDownIcon } from "@chakra-ui/icons";
import { <PERSON>ton, Menu, MenuButton, MenuButtonProps, MenuItemOption, MenuList, MenuOptionGroup } from "@chakra-ui/react";

export type Option = { value: string; text: string };

export type SelectProps = {
  options: Option[];
  onChange?: (event: string | string[]) => void | undefined;
  value?: string | string[];
  selectButtonProps?: MenuButtonProps;
};

export function Select({ options, value, selectButtonProps, onChange }: SelectProps) {
  return (
    <Menu>
      <MenuButton
        color={"white"}
        border={"1px solid #8b8b8bcc"}
        bgColor={"black"}
        _hover={{ bgColor: "black", color: "white" }}
        borderRadius={8}
        textAlign={"left"}
        as={Button}
        rightIcon={<ChevronDownIcon />}
        {...selectButtonProps}
      >
        {options.find((o) => o.value === value)?.text || "Selecciona"}
      </MenuButton>
      <MenuList maxH={500} overflowY="auto">
        <MenuOptionGroup defaultValue={value} value={value} type="radio" onChange={onChange} overflowY="scroll">
          {options.map((o) => (
            <MenuItemOption value={o.value} key={`${o.value}-${o.text}`}>
              {o.text}
            </MenuItemOption>
          ))}
        </MenuOptionGroup>
      </MenuList>
    </Menu>
  );
}
