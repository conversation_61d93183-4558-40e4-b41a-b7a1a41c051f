import { menuAnatomy } from "@chakra-ui/anatomy";
import { createMultiStyleConfigHelpers } from "@chakra-ui/react";

const { definePartsStyle, defineMultiStyleConfig } = createMultiStyleConfigHelpers(menuAnatomy.keys);

// define the base component styles
const baseStyle = definePartsStyle({
  list: {
    bg: "gray.500",
    borderRadius: "12px",
  },
  item: {
    bg: "gray.500",
    _hover: {
      bg: "gray.300",
    },
  },
});
// export the base styles in the component theme
export const MenuTheming = defineMultiStyleConfig({ baseStyle });
