"use client";

import { useSession } from "@app/hooks/useSession";
import { MenuItem, MenuItemProps } from "@chakra-ui/react";
import { DropdownMenu } from "@components/common/Dropdown";
import { UserBadge } from "@components/common/UserBadge";
import { LogoutIcon, SupportIcon } from "@public/svgs";
import { useRouter } from "next/navigation";

const menuItemProps: MenuItemProps = {
  ps: 0,
  pe: 0,
  bg: "transparent",
  alignItems: "center",
  gap: "12px",
};

export function AppDropdownMenu() {
  const router = useRouter();
  const { admin } = useSession();
  const onLogout = () => {
    router.push(`/${admin?.influencerId}/login`);
    localStorage.removeItem("admin");
  };
  return (
    <DropdownMenu buttonContent={<UserBadge />} listProps={{ borderColor: "gray.400 " }}>
      <MenuItem {...menuItemProps} as={"a"} href="mailto:<EMAIL>" display={{ base: "flex", md: "none" }}>
        <SupportIcon />
        Soporte
      </MenuItem>
      <MenuItem {...menuItemProps} onClick={() => onLogout()}>
        <LogoutIcon />
        Cerrar sesión
      </MenuItem>
    </DropdownMenu>
  );
}
