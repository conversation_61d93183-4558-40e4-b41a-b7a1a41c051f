// services/PaymentsService.ts
import { BaseService } from "@app/lib/service/baseService";
import { ppe } from "@mainframe-peru/types";

export class PaymentProviderEventService extends BaseService {
  /**
   * Obtiene el evento del proveedor de pagos
   */
  static async getOne(query: ppe.GetPaymentProviderEventRequest): Promise<ppe.GetPaymentProviderEventResponse> {
    const url = `${this.BASE_URL}/payment-provider-event?${this.buildQueryParams(query)}`;
    const { data } = await this.fetchData<ppe.GetPaymentProviderEventResponse>(url);
    return data;
  }
}
