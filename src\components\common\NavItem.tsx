"use client";

import { NavigationItem } from "@app/lib/types";
import { Flex, ListItem } from "@chakra-ui/react";
import Link from "next/link";
import { usePathname } from "next/navigation";

export function NavItem({ Icon, ...navItemProps }: NavigationItem) {
  const pathName = usePathname();
  const isActive = pathName.includes(navItemProps.to);
  return (
    <ListItem
      padding={"4px 8px"}
      borderRadius={"12px"}
      fontWeight={"bold"}
      display={"flex"}
      alignItems={"center"}
      fontSize={13}
      gap={"12px"}
      color={isActive ? "black" : "whitesmoke"}
      bg={isActive ? "green.300" : "transparent"}
    >
      <Link href={navItemProps.to}>
        <Flex gap={2} alignItems={"center"}>
          {Icon}
          {navItemProps.text}
        </Flex>
      </Link>
    </ListItem>
  );
}
