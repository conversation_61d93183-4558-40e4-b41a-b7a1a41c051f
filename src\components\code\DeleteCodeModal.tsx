import { useSession } from "@app/hooks/useSession";
import { Flex, Heading, Stack, Text, useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { useRouter } from "next/navigation";
import React from "react";
import { Button } from "../common/Button";
import { Modal } from "../common/Modal";
import { CodeService } from "@app/services/codeService";

type DeleteModalProps = {
  id: number;
  isOpen: boolean;
  onClose: () => void;
  onOpen?: () => void;
};

export function DeleteCodeModal({ isOpen, onClose, id }: DeleteModalProps) {
  const [isLoading, setIsLoading] = React.useState(false);

  const session = useSession();
  const router = useRouter();
  const toast = useToast();

  const onDeleteCode = async () => {
    setIsLoading(true);
    try {
      await CodeService.deleteCode({
        id: id,
        all: false,
      });
      onClose();
      setIsLoading(false);
      router.refresh();
    } catch (error) {
      if (error instanceof AppError) {
        toast({
          title: error.code,
          description: ` ${error.message} id: ${session.admin?.id}`,
          status: "error",
          position: "top-right",
          duration: 5000,
          isClosable: true,
        });
        setIsLoading(false);
        onClose();
      }
    }
  };

  return (
    <Modal modalProps={{ isOpen, onClose }}>
      <Stack spacing={5} p={{ base: 8 }}>
        <Heading textAlign={"center"} size={"md"}>
          ¿Estás seguro de querer eliminar este código?
        </Heading>
        <Text textAlign={"center"}>Esta acción es irreversible.</Text>
        <Flex justify={"space-between"} mt={21} flexWrap={{ base: "wrap", md: "nowrap" }} gap={5}>
          <Button
            isLoading={isLoading}
            _hover={{ bgColor: "red.400", color: "white" }}
            onClick={() => onDeleteCode()}
            w={{ base: "100%" }}
          >
            Sí, deseo eliminarlo
          </Button>
          <Button
            isLoading={isLoading}
            variant={"outline"}
            borderColor={"#595959"}
            _hover={{ bgColor: "transparent" }}
            onClick={() => onClose()}
            w={{ base: "100%" }}
          >
            Cancelar
          </Button>
        </Flex>
      </Stack>
    </Modal>
  );
}
