import { EventType } from "@mainframe-peru/types/build/common";
import { Tag } from "../common/Tag";

export type TagMaper<T extends string> = {
  [k in T]: { colorScheme: string; text: string };
};

const eventTypesMapper: TagMaper<EventType | "SPECIAL"> = {
  "IN-PERSON": {
    colorScheme: "blue",
    text: "Reunión",
  },
  GAME: {
    colorScheme: "purple",
    text: "Videojuego",
  },
  PRIZE: {
    colorScheme: "yellow",
    text: "Premio",
  },
  PRODUCT: {
    colorScheme: "pink",
    text: "Producto",
  },
  SPECIAL: {
    colorScheme: "pink",
    text: "Producto",
  },
};

export function EventTypeTag({ type }: { type: EventType }) {
  return <Tag colorScheme={eventTypesMapper[type].colorScheme}>{eventTypesMapper[type].text}</Tag>;
}
