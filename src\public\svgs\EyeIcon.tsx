import { Box, BoxProps } from "@chakra-ui/react";

export function EyeIcon({ width = "24px", height = "24px", ...props }: BoxProps) {
  return (
    <Box {...props} width={width} height={height}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 25" fill="none">
        <path
          d="M21.25 9.65005C18.94 6.02005 15.56 3.93005 12 3.93005C10.22 3.93005 8.49 4.45005 6.91 5.42005C5.33 6.40005 3.91 7.83005 2.75 9.65005C1.75 11.2201 1.75 13.7701 2.75 15.3401C5.06 18.9801 8.44 21.0601 12 21.0601C13.78 21.0601 15.51 20.5401 17.09 19.5701C18.67 18.5901 20.09 17.1601 21.25 15.3401C22.25 13.7801 22.25 11.2201 21.25 9.65005ZM12 16.5401C9.76 16.5401 7.96 14.7301 7.96 12.5001C7.96 10.2701 9.76 8.46005 12 8.46005C14.24 8.46005 16.04 10.2701 16.04 12.5001C16.04 14.7301 14.24 16.5401 12 16.5401Z"
          fill="#737373"
        />
        <path
          d="M12 9.64001C10.43 9.64001 9.14999 10.92 9.14999 12.5C9.14999 14.07 10.43 15.35 12 15.35C13.57 15.35 14.86 14.07 14.86 12.5C14.86 10.93 13.57 9.64001 12 9.64001Z"
          fill="#737373"
        />
      </svg>
    </Box>
  );
}
