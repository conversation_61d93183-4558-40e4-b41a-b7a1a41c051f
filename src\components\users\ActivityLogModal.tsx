"use client";

import { ActivityLogItem } from "@app/lib/activity-log/types";
import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { Box, Flex, Stack, Text, Textarea, useDisclosure } from "@chakra-ui/react";
import { format } from "date-fns";
import React from "react";
import { Modal } from "../common/Modal";
import { Table } from "../common/Table";
import { Tag } from "../common/Tag";
import { Select } from "../common/Select";
import { common } from "@mainframe-peru/types";
import { useUserDetailsPage } from "./UserDetailProvider";

type ActivityLogModalProps = {
  isOpen: boolean;
  onClose: () => void;
  activityLogs: ActivityLogItem[];
  isLoading: boolean;
};

const ActivityStatusTag = ({ status }: { status: "SUCCESS" | "FAIL" }) => {
  const colorScheme = status === "SUCCESS" ? "green" : "red";
  const text = status === "SUCCESS" ? "Exitoso" : "Fallido";

  return <Tag colorScheme={colorScheme}>{text}</Tag>;
};

const ExpandableTextCell = ({ content, title }: { content: string; title: string }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <>
      <Box
        maxWidth="300px"
        overflow="hidden"
        textOverflow="ellipsis"
        whiteSpace="nowrap"
        cursor="pointer"
        _hover={{ textDecoration: "underline" }}
        onClick={onOpen}
      >
        {content}
      </Box>

      <Modal modalProps={{ isOpen, onClose, size: "lg" }} bodyProps={{ p: 6 }} title={title} closable>
        <Textarea value={content} readOnly resize="vertical" minHeight="200px" bg="gray.100" color="black" />
      </Modal>
    </>
  );
};

const statusOptions = [
  { value: "", text: "Todos" },
  { value: "SUCCESS", text: "Exitoso" },
  { value: "FAIL", text: "Fallido" },
];

export function ActivityLogModal({ isOpen, onClose, activityLogs, isLoading }: ActivityLogModalProps) {
  const { fetchActivityLogs } = useUserDetailsPage();
  const [typeFilter, setTypeFilter] = React.useState<string>("");
  const [statusFilter, setStatusFilter] = React.useState<string>("");

  const handleTypeChange = (value: string | string[]) => {
    const newType = Array.isArray(value) ? value[0] : value;
    setTypeFilter(newType);
    fetchActivityLogs({
      type: newType as common.ActivityLogEvent,
      status: statusFilter as "SUCCESS" | "FAIL" | undefined,
    });
  };

  const handleStatusChange = (value: string | string[]) => {
    const newStatus = Array.isArray(value) ? value[0] : value;
    setStatusFilter(newStatus);
    fetchActivityLogs({
      type: typeFilter as common.ActivityLogEvent,
      status: newStatus as "SUCCESS" | "FAIL" | undefined,
    });
  };

  const makeRows = (logs: ActivityLogItem[]) => {
    return logs.map((log) => {
      const createdAt = format(new Date(log.createdAt), TABLE_STANDARD_FORMAT_DATE);

      return [
        log.type,
        <ActivityStatusTag key={log.pk} status={log.status} />,
        <ExpandableTextCell key={`msg-${log.pk}`} content={log.message} title="Mensaje" />,
        <ExpandableTextCell key={`req-${log.pk}`} content={log.request} title="Request" />,
        createdAt,
      ];
    });
  };

  return (
    <Modal modalProps={{ isOpen, onClose, size: "6xl" }} bodyProps={{ p: 6 }} title="Actividad del Usuario" closable>
      <Stack spacing={4}>
        <Flex gap={4} align="center" flexWrap="wrap">
          <Flex align="center" gap={2}>
            <Text fontSize="sm" fontWeight="medium">
              Tipo:
            </Text>
            <Select
              options={common.ActivityLogEventsEnum.options.map((x) => {
                return { value: x, text: x };
              })}
              value={typeFilter}
              onChange={handleTypeChange}
              selectButtonProps={{ minW: 150 }}
            />
          </Flex>
          <Flex align="center" gap={2}>
            <Text fontSize="sm" fontWeight="medium">
              Estado:
            </Text>
            <Select
              options={statusOptions}
              value={statusFilter}
              onChange={handleStatusChange}
              selectButtonProps={{ minW: 120 }}
            />
          </Flex>
        </Flex>

        <Table
          title="Registro de Actividad"
          headers={["Tipo", "Estado", "Mensaje", "Request", "Fecha"]}
          rows={makeRows(activityLogs)}
          isLoading={isLoading}
          boxProps={{ mt: 0 }}
        />
      </Stack>
    </Modal>
  );
}
