"use client";

import { ActivityLogItem } from "@app/lib/activity-log/types";
import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { Box } from "@chakra-ui/react";
import { format } from "date-fns";
import React from "react";
import { Modal } from "../common/Modal";
import { Table } from "../common/Table";
import { Tag } from "../common/Tag";

type ActivityLogModalProps = {
  isOpen: boolean;
  onClose: () => void;
  activityLogs: ActivityLogItem[];
  isLoading: boolean;
};

const ActivityStatusTag = ({ status }: { status: "SUCCESS" | "FAILED" }) => {
  const colorScheme = status === "SUCCESS" ? "green" : "red";
  const text = status === "SUCCESS" ? "Exitoso" : "Fallido";

  return <Tag colorScheme={colorScheme}>{text}</Tag>;
};

export function ActivityLogModal({ isOpen, onClose, activityLogs, isLoading }: ActivityLogModalProps) {
  const makeRows = (logs: ActivityLogItem[]) => {
    return logs.map((log) => {
      const createdAt = format(new Date(log.createdAt), TABLE_STANDARD_FORMAT_DATE);

      return [
        log.type,
        <ActivityStatusTag key={log.pk} status={log.status} />,
        <Box key={log.pk} maxWidth="300px" overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap">
          {log.message}
        </Box>,
        <Box key={log.pk} maxWidth="200px" overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap">
          {log.request}
        </Box>,
        createdAt,
      ];
    });
  };

  return (
    <Modal modalProps={{ isOpen, onClose, size: "6xl" }} bodyProps={{ p: 6 }} title="Actividad del Usuario" closable>
      <Table
        title="Registro de Actividad"
        headers={["Tipo", "Estado", "Mensaje", "Request", "Fecha"]}
        rows={makeRows(activityLogs)}
        isLoading={isLoading}
        boxProps={{ mt: 0 }}
      />
    </Modal>
  );
}
