"use client";

import { TransactionService } from "@app/services/transactionService";
import { TRANSACTION_ACTIONS } from "@app/stores/Transactions/constants";
import { transactionsReducer } from "@app/stores/Transactions/reducers";
import { TRANSACTIONS_PAGE_STORE } from "@app/stores/Transactions/store";
import { TransactionsActions, TransactionsPageStore } from "@app/stores/Transactions/types";
import { useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { TransactionState } from "@mainframe-peru/types/build/common";
import React from "react";

export const TransactionsPageContext = React.createContext({} as TransactionsPageStore);
export const TransactionsPageDispatchContext = React.createContext({} as React.Dispatch<TransactionsActions>);

export function TransactionsPageProvider({ children }: React.PropsWithChildren) {
  const [store, dispatch] = React.useReducer(transactionsReducer, TRANSACTIONS_PAGE_STORE);
  const toast = useToast();

  const fetchTransctions = React.useCallback(async () => {
    try {
      const transactions = await TransactionService.getTransactions({ detailed: true });
      dispatch({
        type: TRANSACTION_ACTIONS.LOAD_TRANSACTIONS,
        payload: {
          isLoading: false,
          transactions,
        },
      });
    } catch (error) {
      dispatch({
        type: TRANSACTION_ACTIONS.LOAD_TRANSACTIONS,
        payload: {
          isLoading: false,
        },
      });
      if (error instanceof AppError) {
        toast({ status: "error", title: error.name, description: error.message, isClosable: true });
      }
    }
  }, [toast]);

  const fetchTransactionsByState = React.useCallback(
    async (state: TransactionState) => {
      try {
        const transactions = await TransactionService.getTransactions({
          detailed: true,
          state,
        });
        dispatch({
          type: TRANSACTION_ACTIONS.LOAD_TRANSACTIONS,
          payload: {
            isLoading: false,
            transactions,
          },
        });
      } catch (error) {
        dispatch({
          type: TRANSACTION_ACTIONS.LOAD_TRANSACTIONS,
          payload: {
            isLoading: false,
          },
        });
        if (error instanceof AppError) {
          toast({ status: "error", title: error.name, description: error.message });
        }
      }
    },
    [toast],
  );

  React.useEffect(() => {
    dispatch({
      type: TRANSACTION_ACTIONS.LOAD_TRANSACTIONS,
      payload: {
        isLoading: true,
      },
    });

    if (!store.filters.status) {
      fetchTransctions();
      return;
    }

    fetchTransactionsByState(store.filters.status);
  }, [store.filters.status, fetchTransctions, fetchTransactionsByState]);

  return (
    <TransactionsPageContext.Provider value={store}>
      <TransactionsPageDispatchContext.Provider value={dispatch}>{children}</TransactionsPageDispatchContext.Provider>
    </TransactionsPageContext.Provider>
  );
}
