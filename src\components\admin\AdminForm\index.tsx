"use client";

import { Button } from "@app/components/common/Button";
import { useSession } from "@app/hooks/useSession";
import { useToast } from "@app/hooks/useToast";
import { AdminService } from "@app/services/adminService";
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Skeleton,
} from "@chakra-ui/react";
import { AdminPolicies, AppError, Policies, adminPoliciesConstant } from "@mainframe-peru/common-core";
import { admin as at } from "@mainframe-peru/types";
import { useRouter } from "next/navigation";
import React from "react";

const policyModulesNameMap: Record<keyof AdminPolicies, string> = {
  admin: "Admin",
  card: "Tarjetas",
  complaint: "Reclamos",
  event: "Eventos",
  influencer: "Influencers",
  invoice: "Boletas/Facturas",
  paymentProviderEvent: "Eventos de pago",
  plan: "Planes",
  recurrence: "Cobro recurrente",
  transaction: "Transacciones",
  user: "Usuarios",
  business: "Negocios",
  businessPromotion: "Promociones",
  businessPromotionCode: "Códigos",
  code: "Códigos de regalo",
  pchujoyGame: "Juegos",
};
type AdminEntity = Omit<at.CreateAdminRequest, "policies" | "influencerId" | "password"> & { id?: number };

export function AdminForm(props: { edit?: true }) {
  const { fire } = useToast();
  const router = useRouter();
  const { influencer } = useSession();
  const [loading, setLoading] = React.useState(!!props.edit);
  const [saving, setSaving] = React.useState(false);
  const [policies, setPolicies] = React.useState<Record<string, Record<string, boolean>>>(
    Policies.getUnmaskedDefaultPolicies(adminPoliciesConstant),
  );
  const [confirmDelete, setConfirmDelete] = React.useState(false);
  const [admin, setAdmin] = React.useState<AdminEntity>({
    email: "",
    firstName: "",
    lastName: "",
  });
  const timeout = React.useRef<NodeJS.Timeout>();

  React.useEffect(() => {
    if (props.edit) {
      const urlParams = new URLSearchParams(location.search);
      const id = urlParams.get("id");
      if (!id) {
        throw new AppError({ code: "MissingId", message: "Edit admin requires id" });
      }
      loadAdmin(Number(id));
    }
  }, [props.edit]);

  React.useEffect(() => {
    return () => {
      if (timeout.current) clearTimeout(timeout.current);
    };
  }, []);

  async function loadAdmin(id: number) {
    const response = await AdminService.getOne({ id });
    setAdmin({
      id: response.id,
      firstName: response.firstName,
      lastName: response.lastName,
      email: response.email,
    });
    setPolicies(Policies.unmask(response.policies, adminPoliciesConstant));
    setLoading(false);
  }

  async function submitAdmin() {
    if ([!admin.firstName, !admin.lastName, !admin.email].includes(true)) {
      fire("Faltan datos", "Se debe completar todos los campos", "error");
      return;
    }
    setSaving(true);
    try {
      if (admin.id) {
        await AdminService.update({
          id: admin.id,
          email: admin.email,
          firstName: admin.firstName,
          lastName: admin.lastName,
          policies: Policies.mask(policies, adminPoliciesConstant),
        });
        fire("¡Éxito!", "El admin se actualizó correctamente.", "success");
      } else {
        await AdminService.create({
          email: admin.email,
          firstName: admin.firstName,
          lastName: admin.lastName,
          policies: Policies.mask(policies, adminPoliciesConstant),
        });
        fire("¡Éxito!", "El admin se creó correctamente.", "success");
        router.push(`/${influencer.id}/app/admins`);
      }
    } catch (e) {
      if (e instanceof AppError) fire(e.code, e.message, "error");
    }
    setSaving(false);
  }

  async function deleteAdmin() {
    setSaving(true);
    try {
      if (admin.id) {
        await AdminService.delete(admin.id);
        fire("¡Éxito!", "El admin se eliminó correctamente.", "success");
        router.push(`/${influencer.id}/app/admins`);
      }
    } catch (e) {
      if (e instanceof AppError) fire(e.code, e.message, "error");
    }
  }

  async function preDeleteAdmin() {
    setConfirmDelete(true);
    timeout.current = setTimeout(() => setConfirmDelete(false), 5000);
  }

  function togglePolicy(module: string, policy: string): void {
    const modulePolicies = policies[module as keyof AdminPolicies] as Record<string, boolean>;
    const newPolicies = {
      ...policies,
      [module]: {
        ...modulePolicies,
        [policy]: !modulePolicies[policy],
      },
    };
    setPolicies(newPolicies);
  }

  return (
    <Flex bg="black" p="32px" gap="32px" borderRadius="22px" position="relative" height="800px" alignItems="flex-start">
      <Skeleton isLoaded={!loading} flex={1} gap="8px" display="flex" flexDirection="column">
        <FormControl>
          <FormLabel fontSize={13}>Nombres</FormLabel>
          <Input
            bgColor="#262626"
            value={admin.firstName}
            onChange={(e) => setAdmin({ ...admin, firstName: e.target.value })}
            placeholder="Nombres"
            _placeholder={{ color: "gray.100" }}
          />
        </FormControl>
        <FormControl>
          <FormLabel fontSize={13}>Apellidos</FormLabel>
          <Input
            bgColor="#262626"
            value={admin.lastName}
            onChange={(e) => setAdmin({ ...admin, lastName: e.target.value })}
            placeholder="Apellidos"
            _placeholder={{ color: "gray.100" }}
          />
        </FormControl>
        <FormControl>
          <FormLabel fontSize={13}>Email</FormLabel>
          <Input
            bgColor="#262626"
            value={admin.email}
            onChange={(e) => setAdmin({ ...admin, email: e.target.value })}
            placeholder="Email"
            _placeholder={{ color: "gray.100" }}
          />
        </FormControl>
      </Skeleton>
      <Skeleton flex={1} isLoaded={!loading}>
        <Heading as="h6" size="md" mb="8px">
          Permisos
        </Heading>
        <Accordion allowToggle>
          {Object.entries(adminPoliciesConstant).map(([mk, m]) => (
            <AccordionItem key={mk}>
              <AccordionButton>
                <Box>{policyModulesNameMap[mk as keyof AdminPolicies]}</Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel pb={4}>
                {Object.entries(m).map(([pk, p]) => (
                  <Flex key={pk}>
                    <Box mr="12px">
                      <Checkbox
                        size="md"
                        isChecked={policies[mk][pk]}
                        onChange={() => togglePolicy(mk, pk)}
                        colorScheme="green"
                      />
                    </Box>
                    <Box>
                      {pk}: {p.description}
                    </Box>
                  </Flex>
                ))}
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </Skeleton>
      <Flex position="absolute" bottom="32px" right="32px" gap="16px">
        {props.edit && (
          <Button
            isLoading={loading || saving}
            onClick={confirmDelete ? deleteAdmin : preDeleteAdmin}
            variant="outline"
            colorScheme="red"
            borderColor="red.600"
            color="red.600"
            _hover={{ backgroundColor: "red.600", color: "white" }}
          >
            {confirmDelete ? "¿Seguro?" : "Eliminar"}
          </Button>
        )}
        <Button isLoading={loading || saving} onClick={submitAdmin}>
          Guardar
        </Button>
      </Flex>
    </Flex>
  );
}
