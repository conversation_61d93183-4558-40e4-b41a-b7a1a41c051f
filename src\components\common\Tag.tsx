import { Tag as ChakraTag, TagLabel, TagProps as ChakraTagProps } from "@chakra-ui/react";
import React from "react";

type TagProps = {
  children: React.ReactNode;
} & ChakraTagProps;

export function Tag({ children, ...rest }: TagProps) {
  return (
    <ChakraTag borderRadius={"full"} variant={"solid"} {...rest}>
      <TagLabel fontWeight={"bold"} textTransform={"capitalize"}>
        {children}
      </TagLabel>
    </ChakraTag>
  );
}
