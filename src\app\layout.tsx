import { ThemeProvider } from "@app/contexts/ThemeProvider";
import type { Metadata } from "next";
import { Lexend_Deca } from "next/font/google";

const lextendDeca = Lexend_Deca({ weight: ["300", "400", "700", "900"], subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${lextendDeca.className}`} style={{ height: "100dvh" }}>
        <ThemeProvider>{children}</ThemeProvider>
      </body>
    </html>
  );
}
