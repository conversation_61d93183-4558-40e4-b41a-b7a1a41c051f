"use client";

import { But<PERSON> } from "@app/components/common/Button";
import { Flex, Heading, useDisclosure } from "@chakra-ui/react";
import { UploadCodesModal } from "./UploadCodesModal";

export function CodeToolbar({ promotionId }: { promotionId: number }) {
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <Flex justifyContent="space-between" m={4}>
      <Flex alignItems={"center"} justify={"space-between"}>
        <Heading size={"md"} color={"#5C73F2"}>
          Códigos
        </Heading>
      </Flex>
      <Button onClick={onOpen}><PERSON><PERSON>r código</Button>
      <UploadCodesModal isOpen={isOpen} onClose={onClose} promotionId={promotionId}></UploadCodesModal>
    </Flex>
  );
}
