import { Flex, FlexProps } from "@chakra-ui/react";
import { PaymentsStatusToggler } from "./TransactionsStatusToggler";
import { DownloadTransactionList } from "./DownloadTransactionList";

export const flexGrowContainer: FlexProps = {
  flexGrow: {
    base: 1,
    md: 0,
  },
};

export function TransactionsToolbar() {
  return (
    <Flex justifyContent={"space-between"} alignItems={"center"} flexWrap={{ base: "wrap", md: "nowrap" }} gap={34}>
      <PaymentsStatusToggler {...flexGrowContainer} />
      <DownloadTransactionList />
    </Flex>
  );
}
