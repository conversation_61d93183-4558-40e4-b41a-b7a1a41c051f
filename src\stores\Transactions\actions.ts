import { limitTransactions } from "@app/lib/transactions/utils";
import {
  SetCurrentTransactionAction,
  TransactionsChangeDateAction,
  TransactionsChangeLimitsRowsAction,
  TransactionsChangeStatusAction,
  TransactionsLoadAction,
  TransactionsPageStore,
} from "./types";

export function changeStatus(
  action: TransactionsChangeStatusAction,
  state: TransactionsPageStore,
): TransactionsPageStore {
  if (state.filters.status === action.payload.status) {
    return {
      ...state,
      filters: {
        ...state.filters,
        status: undefined,
      },
    };
  }
  return {
    ...state,
    filters: {
      ...state.filters,
      status: action.payload.status,
    },
  };
}

export function changeLimit(
  action: TransactionsChangeLimitsRowsAction,
  state: TransactionsPageStore,
): TransactionsPageStore {
  const transactionsFiltered = limitTransactions(state.transactionsFiltered, action.payload.limit);
  return {
    ...state,
    transactionsFiltered,
    filters: {
      ...state.filters,
      limit: action.payload.limit,
    },
  };
}

export function loadTransactions(action: TransactionsLoadAction, state: TransactionsPageStore): TransactionsPageStore {
  const transactions = action.payload.transactions || [];
  return {
    ...state,
    transactions,
    isLoading: action.payload.isLoading,
  };
}

export function changeDate(action: TransactionsChangeDateAction, state: TransactionsPageStore): TransactionsPageStore {
  const [[target, value]] = Object.entries(action.payload);
  return {
    ...state,
    filters: {
      ...state.filters,
      date: {
        ...state.filters.date,
        [target]: value,
      },
    },
  };
}

export function setCurrentTransaction(
  action: SetCurrentTransactionAction,
  state: TransactionsPageStore,
): TransactionsPageStore {
  return {
    ...state,
    currentTransaction: action.payload.currentTransaction,
  };
}
