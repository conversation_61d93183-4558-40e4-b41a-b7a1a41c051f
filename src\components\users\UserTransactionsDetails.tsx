"use client";

import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { Transaction } from "@app/lib/transactions/types";
import { getCurrencyFormat } from "@app/lib/transactions/utils";
import { Box, Button, Heading, Skeleton, useDisclosure } from "@chakra-ui/react";
import { format } from "date-fns";
import { Table } from "../common/Table";
import { TransactionStatusTag } from "../transactions/TransactionStatusTag";
import { useUserDetailsPage } from "./UserDetailProvider";
import { EyeIcon } from "@app/public/svgs/EyeIcon";
import { DetailModal } from "../transactions/TransactionDetailModal";
import { useState } from "react";

export function UserTransactionsDetails() {
  const { transactions, isLoading } = useUserDetailsPage();
  const { isOpen, onClose, onOpen } = useDisclosure();
  const [transactionCurrent, setTransactionCurrent] = useState<Transaction | undefined>();

  const onModalOpen = (transaction: Transaction) => {
    setTransactionCurrent(transaction);
    onOpen();
  };

  function ViewButton({ onOpen }: { onOpen: () => void }) {
    return (
      <Button onClick={onOpen} bgColor={"transparent"} _hover={{ bgColor: "transparent" }}>
        <EyeIcon />
      </Button>
    );
  }

  const makeRows = (transactions: Transaction[]) => {
    return transactions.map((transaction) => {
      const createdAtFormated = format(transaction.createdAt, TABLE_STANDARD_FORMAT_DATE);
      const amountFormated = getCurrencyFormat(transaction.currency, transaction.amount);

      return [
        transaction.id,
        createdAtFormated,
        <TransactionStatusTag status={transaction.state} key={transaction.id} />,
        amountFormated,
        <ViewButton key={transaction.id} onOpen={() => onModalOpen(transaction)} />,
      ];
    });
  };

  return (
    <Skeleton isLoaded={!isLoading} borderRadius={21}>
      <Box bg="black" color="white" borderRadius={21} p={6}>
        <Heading mb={8} size={"md"} color={"#5C73F2"}>
          Últimas 100 Transacciones
        </Heading>
        <Table
          boxProps={{ padding: 0, mt: 34 }}
          headers={["Id", "Fecha", "Estado", "Total", ""]}
          rows={makeRows(transactions)}
          isLoading={isLoading}
        />
        <DetailModal isOpen={isOpen} onClose={onClose} transaction={transactionCurrent} />
      </Box>
    </Skeleton>
  );
}
