import { Box, BoxProps } from "@chakra-ui/react";

export function PlusIcon({ width = "6px", height = "6px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 6 7" fill="none">
        <path d="M2.25 0.5H3.75V6.5H2.25V0.5Z" fill="#0D0D0D" />
        <path d="M6 2.75V4.25L0 4.25L6.55671e-08 2.75L6 2.75Z" fill="#0D0D0D" />
      </svg>
    </Box>
  );
}
