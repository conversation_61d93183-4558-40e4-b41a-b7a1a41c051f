"use client";

import { Box, useDisclosure, UseDisclosureReturn } from "@chakra-ui/react";
import { eachDayOfInterval, EachDayOfIntervalResult, endOfMonth, endOfWeek, startOfMonth, startOfWeek } from "date-fns";
import React, { PropsWithChildren } from "react";
import { DatepickerProps } from ".";

export type DatepickerContextValues = {
  calendarDays: EachDayOfIntervalResult<
    {
      start: Date;
      end: Date;
    },
    undefined
  >;
  currentDate?: Date;
  onChangeDate: (date: Date) => void;
  onResetDate: () => void;
  type?: "date" | "datetime-local";
} & Pick<UseDisclosureReturn, "getDisclosureProps" | "isOpen" | "onClose" | "onOpen">;

export const useDatepicker = () => React.useContext(DatepickerContext);
export const DatepickerContext = React.createContext({} as DatepickerContextValues);

export function DatepickerProvider({ children, ...props }: PropsWithChildren<DatepickerProps>) {
  const { onClose, onOpen, getDisclosureProps, isOpen } = useDisclosure();
  const { onChange, onReset, date: currentDate } = props;

  const onChangeDate = (date: Date) => {
    if (onChange) onChange(date);
  };

  const onResetDate = () => {
    if (onReset) onReset();
  };

  const calendarDays = eachDayOfInterval({
    start: startOfWeek(startOfMonth(currentDate ?? new Date())),
    end: endOfWeek(endOfMonth(currentDate ?? new Date())),
  });

  return (
    <DatepickerContext.Provider
      value={{
        calendarDays,
        currentDate,
        isOpen,
        type: props.type,
        onChangeDate,
        onOpen,
        onClose,
        getDisclosureProps,
        onResetDate,
      }}
    >
      <Box minW={0} position="relative" _hover={{ cursor: "pointer" }}>
        {children}
      </Box>
    </DatepickerContext.Provider>
  );
}
