"use client";

import { Button } from "@app/components/common/Button";
import { Datepicker } from "@app/components/common/Datepicker";
import { ImageDropzone } from "@app/components/common/ImageDropzone";
import { Option, Select } from "@app/components/common/Select";
import { useEventForm } from "@app/hooks/useEventForm";
import { Flex, FormControl, FormLabel, Heading, Input, Skeleton, Stack, Text, Textarea } from "@chakra-ui/react";
import { EventStatus, EventType } from "@mainframe-peru/types/build/common";
import { defaultDate } from "./EventFormProvider";
import { EventAttributes } from "./EventAttributes";

const eventTypes: Option[] = [
  { value: "PRIZE", text: "Premio" },
  { value: "PRODUCT", text: "Producto" },
  { value: "IN-PERSON", text: "Reunión" },
  { value: "GAME", text: "Videojuego" },
];

const eventStatus: Option[] = [
  { value: "ACTIVE", text: "Activo" },
  { value: "INACTIVE", text: "Inactivo" },
];

export function EventForm({ isEditing }: { isEditing?: boolean }) {
  const { event, isSeasonEvent, isLoading, loadSkeleton, onDrop, onSubmitEventForm, onChangeEvent } = useEventForm();

  const onChangeDate = (key: "startDate" | "endDate" | "eventDate") => {
    return (date: Date) => {
      onChangeEvent({ [key as string]: date });
    };
  };

  const onResetDate = (key: "startDate" | "endDate" | "eventDate") => {
    return () => {
      onChangeEvent({ [key as string]: defaultDate });
    };
  };

  return (
    <Stack as="form" gap={5} onSubmit={onSubmitEventForm}>
      <Flex align="center" gap={5} mt={21} flexDir={{ base: "column", md: "row" }} style={{ alignItems: "flex-start" }}>
        <Skeleton isLoaded={!loadSkeleton} flexBasis="100%" w={{ base: "100%", md: "100%" }} borderRadius={21}>
          <Stack gap={5} bgColor="black" borderRadius={21} p={5}>
            <Flex alignItems={"center"} justify={"space-between"}>
              <Heading size={"md"} color={"blue.500"}>
                Detalles del evento
              </Heading>
            </Flex>
            <Flex gap={3} flexBasis="100%" justifyContent="flex-start" flexDir={{ base: "column", md: "row" }}>
              <FormControl minW={0} w={["100%", "100%", 300]}>
                <FormLabel fontSize={13}>Fecha del evento</FormLabel>
                <Datepicker
                  date={event.eventDate || defaultDate}
                  onChange={onChangeDate("eventDate")}
                  onReset={onResetDate("eventDate")}
                  styles={{ backgroundColor: "#262626" }}
                />
              </FormControl>
              {isSeasonEvent && (
                <>
                  <FormControl minW={0} w={["100%", "100%", 300]}>
                    <FormLabel fontSize={13}>Fecha de inicio del evento</FormLabel>
                    <Datepicker
                      date={event.startDate || defaultDate}
                      onChange={onChangeDate("startDate")}
                      onReset={onResetDate("startDate")}
                      styles={{ backgroundColor: "#262626" }}
                    />
                  </FormControl>
                  <FormControl minW={0} w={["100%", "100%", 300]}>
                    <FormLabel fontSize={13}>Fecha de fin del evento</FormLabel>
                    <Datepicker
                      date={event.endDate || defaultDate}
                      onChange={onChangeDate("endDate")}
                      onReset={onResetDate("endDate")}
                      styles={{ backgroundColor: "#262626" }}
                    />
                  </FormControl>
                </>
              )}
            </Flex>
            <Flex gap={3} flexDir={{ base: "column", md: "row" }}>
              <FormControl>
                <FormLabel fontSize={13}>Nombre del evento</FormLabel>
                <Input
                  bgColor="#262626"
                  border="1px solid gray.300"
                  value={event.name}
                  onChange={(e) => onChangeEvent({ name: e.target.value })}
                  placeholder="Ingresa el nombre del evento"
                  _placeholder={{ color: "gray.100" }}
                />
              </FormControl>
            </Flex>
            <FormControl>
              <FormLabel fontSize={13}>Descripción del evento</FormLabel>
              <Textarea
                value={event.description}
                bgColor="#262626"
                onChange={(e) => onChangeEvent({ description: e.target.value })}
                borderRadius={13}
                minH={150}
                placeholder="Descripción del evento"
                _placeholder={{ color: "gray.100" }}
                fontSize={13}
                borderColor="black"
              />
            </FormControl>
            <Flex gap={3} flexDir={{ base: "column", lg: "row" }}>
              <Stack flex="1" minW={0}>
                <Text fontSize={13}>Tipo de evento</Text>
                <Select
                  value={event.type}
                  onChange={(e) => onChangeEvent({ type: e as EventType })}
                  options={eventTypes}
                  selectButtonProps={{ w: "100%" }}
                />
              </Stack>
              <Stack flex="1" minW={0}>
                <Text fontSize={13}>Tipo de evento</Text>
                <Select
                  value={event.status}
                  onChange={(e) => onChangeEvent({ status: e as EventStatus })}
                  options={eventStatus}
                  selectButtonProps={{ w: "100%" }}
                />
              </Stack>
            </Flex>
            <Stack>
              <Text fontSize={13}>Imagen de portada</Text>
              <ImageDropzone onDrop={onDrop} previewUrl={event.imageUrl} />
            </Stack>
            <EventAttributes />
            <Button type="submit" isLoading={isLoading}>
              {isEditing ? "Editar evento" : "Crear evento"}
            </Button>
          </Stack>
        </Skeleton>
      </Flex>
    </Stack>
  );
}
