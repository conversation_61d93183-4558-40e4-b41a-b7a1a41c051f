import { Box, BoxProps } from "@chakra-ui/react";

export function CardIcon({ width = "24px", height = "24px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none">
        <path
          d="M22 21.25C22 21.66 21.66 22 21.25 22H2.75C2.34 22 2 21.66 2 21.25C2 20.84 2.34 20.5 2.75 20.5H21.25C21.66 20.5 22 20.84 22 21.25Z"
          fill="currentColor"
        />
        <path
          d="M15.3899 4.52001L4.64994 15.26C4.23994 15.67 3.57994 15.67 3.17994 15.26H3.16994C1.77994 13.86 1.77994 11.6 3.16994 10.21L10.3199 3.06001C11.7199 1.66001 13.9799 1.66001 15.3799 3.06001C15.7899 3.45001 15.7899 4.12001 15.3899 4.52001Z"
          fill="currentColor"
        />
        <path
          d="M20.8199 8.49007L17.7699 5.44007C17.3599 5.03007 16.6999 5.03007 16.2999 5.44007L5.55994 16.1801C5.14994 16.5801 5.14994 17.2401 5.55994 17.6501L8.60994 20.7101C10.0099 22.1001 12.2699 22.1001 13.6699 20.7101L20.8099 13.5601C22.2299 12.1601 22.2299 9.89007 20.8199 8.49007ZM12.7599 17.5201L11.5499 18.7401C11.2999 18.9901 10.8899 18.9901 10.6299 18.7401C10.3799 18.4901 10.3799 18.0801 10.6299 17.8201L11.8499 16.6001C12.0899 16.3601 12.5099 16.3601 12.7599 16.6001C13.0099 16.8501 13.0099 17.2801 12.7599 17.5201ZM16.7299 13.5501L14.2899 16.0001C14.0399 16.2401 13.6299 16.2401 13.3699 16.0001C13.1199 15.7501 13.1199 15.3401 13.3699 15.0801L15.8199 12.6301C16.0599 12.3901 16.4799 12.3901 16.7299 12.6301C16.9799 12.8901 16.9799 13.3001 16.7299 13.5501Z"
          fill="currentColor"
        />
      </svg>
    </Box>
  );
}
