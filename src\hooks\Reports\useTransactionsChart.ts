import { TransactionsChartContext, TransactionsChartDispatch } from "@app/contexts/Reports/TransactionsChart/provider";
import React from "react";

export const useTransactionsChart = () => {
  const transactionsChartStore = React.useContext(TransactionsChartContext);
  const transactionsChartDispatch = React.useContext(TransactionsChartDispatch);
  return {
    transactionsChartStore,
    transactionsChartDispatch,
  };
};
