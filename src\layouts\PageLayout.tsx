import { Breadcrumb, BreadCrumbItem } from "@app/components/common/Breadcrumb";
import { Heading, Skeleton, Flex, Box } from "@chakra-ui/react";
import React, { PropsWithChildren } from "react";

export type PageLayoutProps = {
  breadcrumb: BreadCrumbItem;
  title: string;
  isLoading?: boolean;
  titleLeftSlot?: React.ReactNode;
  titleRightSlot?: React.ReactNode;
} & PropsWithChildren;

export function PageLayout({ children, breadcrumb, title, isLoading, titleLeftSlot, titleRightSlot }: PageLayoutProps) {
  return (
    <React.Fragment>
      <Skeleton isLoaded={!isLoading} maxW={300} borderRadius={13}>
        <Breadcrumb breadCrumbItem={breadcrumb} />
      </Skeleton>
      <Skeleton isLoaded={!isLoading} borderRadius={13} mb={5}>
        <Flex align="center" gap={4}>
          {titleLeftSlot && <Box>{titleLeftSlot}</Box>}
          <Heading flex="1" color="green.300">
            {title}
          </Heading>
          {titleRightSlot && <Box>{titleRightSlot}</Box>}
        </Flex>
      </Skeleton>
      {children}
    </React.Fragment>
  );
}
