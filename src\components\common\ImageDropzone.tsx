"use client";

import { useToast } from "@app/hooks/useToast";
import { UploadFileIcon } from "@app/public/svgs/UploadFileIcon";
import { Flex, FlexProps, Image } from "@chakra-ui/react";
import { useDropzone } from "react-dropzone";

const imgAreaProps: FlexProps = {
  bgColor: "transparent",
  borderWidth: 2,
  borderColor: "#8b8b8bcc",
  borderStyle: "dashed",
  bg: "gray.400",
  w: "100%",
  minH: 300,
  h: "100%",
  borderRadius: 8,
  p: 8,
  cursor: "pointer",
  justify: "center",
  align: "center",
  flexDir: "column",
  gap: 5,
};

export type ImageDropzoneProps = {
  onDrop: (acceptedFiles: File[]) => void;
  previewUrl?: string;
};

export function ImageDropzone(props: ImageDropzoneProps) {
  const { fire } = useToast();
  const { getInputProps, getRootProps, isDragActive, isFocused, fileRejections } = useDropzone({
    onDrop: (acceptedFiles: File[]) => {
      // 2MB limit 2000000
      if (acceptedFiles[0].size > 2000000) {
        fire("Error de formulario", "El peso de la imagen excede al máximo recomendado 2MB", "error");
        return;
      }

      props.onDrop(acceptedFiles);
    },
    accept: { "image/png": [], "image/jpeg": [], "image/webp": [] },
    multiple: false,
  });

  return (
    <Flex
      {...imgAreaProps}
      position={"relative"}
      borderColor={fileRejections.length > 0 ? "red.500" : isFocused || isDragActive ? "blue.300" : "#A6A6A6"}
      {...getRootProps()}
    >
      <input {...getInputProps()} />
      {props.previewUrl ? (
        <Image src={props.previewUrl} alt={"image preview"} borderRadius="10px" maxHeight="200px" />
      ) : isDragActive ? (
        "Déjalo caer aquí."
      ) : (
        <>
          <UploadFileIcon w={34} h={34} />
          Arrastra o haz clic para subir tu imagen.
        </>
      )}
    </Flex>
  );
}
