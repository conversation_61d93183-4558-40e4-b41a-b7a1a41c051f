import { common } from "@mainframe-peru/types";
import { Tag } from "../common/Tag";

export function ComplaintStatusTag({ status }: { status: common.SupportStatus }) {
  const state: { [k: string]: { colorScheme: string; text: string } } = {
    OPENED: {
      colorScheme: "green",
      text: "Abierto",
    },
    CLOSED: {
      colorScheme: "blue",
      text: "Cerrado",
    },
    ARCHIVED: {
      colorScheme: "purple",
      text: "Archivado",
    },
  };
  const colorScheme = state[status || "CLOSED"].colorScheme;
  const text = state[status || "OPENED"].text;
  return <Tag colorScheme={colorScheme}>{text}</Tag>;
}
