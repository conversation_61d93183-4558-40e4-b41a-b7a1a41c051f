import { ListUsersResponse } from "@mainframe-peru/types/build/user";
import { ActionSchema } from "../types";
import { USERS_PAGE_ACTION } from "./constants";

export type UsersPageStore = {
  users: ListUsersResponse;
  isLoading: boolean;
  usersFiltered: [];
  filters: {
    status: "SUBSCRIBE" | "NOT_SUBSCRIBE" | undefined;
  };
};

// ACTIONS FOR REDUCER
export type UsersLoadAction = ActionSchema<
  USERS_PAGE_ACTION.LOAD_USERS,
  { isLoading: boolean; users?: ListUsersResponse }
>;

export type UsersPageActions = UsersLoadAction;
