"use client";

import { ViewIcon, ViewOffIcon } from "@chakra-ui/icons";
import {
  Alert,
  AlertIcon,
  Box,
  BoxProps,
  ButtonProps,
  Flex,
  FormControl,
  FormLabel,
  Input,
  InputGroup,
  InputRightElement,
  Text,
} from "@chakra-ui/react";

import { AuthService } from "@app/services/authService";
import { Button } from "@components/common/Button";
import { EMAIL_REGEX_PATTERN } from "@lib/common/constants";
import { LoginAdminRequest } from "@mainframe-peru/types/build/admin";
import { ArrowIcon } from "@public/svgs/ArrowIcon";
import { useRouter } from "next/navigation";
import React from "react";
import { useForm } from "react-hook-form";

const formProps: BoxProps = {
  as: "form",
  width: "100%",
  marginTop: "20px",
  display: "flex",
  flexDir: "column",
  gap: "12px",
};

const labelProps = {
  fontSize: "12px",
  fontWeight: "bold",
};

const buttonProps: ButtonProps = {
  type: "submit",
  width: "100%",
  marginTop: "10px",
  color: "black",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  leftIcon: <ArrowIcon />,
  gap: "5px",
};

type LoginFormValues = {
  email: string;
  password: string;
};

type LoginFormProps = {
  influencerId: string;
};

function LoginForm({ influencerId }: LoginFormProps) {
  const [show, setShow] = React.useState(false);
  const [error, setError] = React.useState(false);
  const [message, setMessage] = React.useState("");
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const { register, handleSubmit, formState } = useForm<LoginFormValues>({});
  const router = useRouter();

  // login to the admin dashboard
  const onSubmit = async (formValues: LoginFormValues) => {
    setMessage("");
    setError(false);
    setIsLoading(true);

    const payload: LoginAdminRequest = { ...formValues, influencerId };
    const response = await AuthService.loginAdmin(payload);
    const data = await response.json();
    localStorage.setItem("admin", JSON.stringify(data));

    if (response.status === 403) {
      setMessage("No hay un usuario registrado con estas credenciales");
      setError(true);
      setIsLoading(false);
      return;
    }

    if (!response.ok) {
      setMessage("Hubo un problema intentando ingresar, contacte a soporte");
      setError(true);
      setIsLoading(false);
      return;
    }

    router.push(`/${influencerId}/app/dashboard`);
  };

  return (
    <Box {...formProps} onSubmit={handleSubmit(onSubmit)}>
      <FormControl isRequired>
        <FormLabel {...labelProps}>Correo o usuario</FormLabel>
        <Input
          autoComplete="username"
          isInvalid={!!formState.errors.email}
          {...register("email", { pattern: EMAIL_REGEX_PATTERN })}
        />
        <Text mt="5px" {...labelProps} color="red.300" hidden={!formState.errors.email}>
          Debe ser un email válido
        </Text>
      </FormControl>

      <FormControl isRequired>
        <FormLabel {...labelProps}>Contraseña</FormLabel>
        <InputGroup>
          <Input
            autoComplete="current-password"
            isInvalid={!!formState.errors.password}
            type={show ? "text" : "password"}
            {...register("password", { minLength: 2 })}
          />
          <InputRightElement bg="transparent">
            <Flex
              role="button"
              cursor={"pointer"}
              onClick={() => setShow(!show)}
              bg={"black"}
              justifyContent={"center"}
              alignItems={"center"}
            >
              {show ? <ViewIcon /> : <ViewOffIcon />}
            </Flex>
          </InputRightElement>
        </InputGroup>
        <Text mt="5px" {...labelProps} color="red.300" hidden={!formState.errors.password}>
          Máximo 2 caracteres
        </Text>
      </FormControl>

      <Flex fontSize={"12px"} flexDir={"column"} justifyContent={"center"} alignItems={"center"} gap={1}>
        <Button {...buttonProps} isLoading={isLoading} loadingText="Cargando" spinnerPlacement="end">
          Ingresar
        </Button>
        {error && (
          <Alert
            status="error"
            mt={5}
            variant="left-accent"
            bgColor={"red.500"}
            fontWeight={"bold"}
            opacity={"0.8"}
            color={"white"}
          >
            <AlertIcon color={"white"} />
            {message}
          </Alert>
        )}
      </Flex>
    </Box>
  );
}

export { LoginForm };
