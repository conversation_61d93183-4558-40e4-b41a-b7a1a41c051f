import { BaseService } from "@app/lib/service/baseService";
import { LoginAdminRequest } from "@mainframe-peru/types/build/admin";

export class AuthService extends BaseService {
  static async loginAdmin(payload: LoginAdminRequest) {
    const response = await fetch(`${this.BASE_URL}/admin/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify(payload),
    });

    return response;
  }
}
