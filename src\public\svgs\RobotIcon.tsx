import { Box, BoxProps } from "@chakra-ui/react";

export function RobotIcon({ width = "24px", height = "24px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg width="100%" height="100%" viewBox="0 0 144 144" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M102 120H66C46.2 120 30 103.8 30 84C30 64.2 46.2 48 66 48H102C121.8 48 138 64.2 138 84C138 103.8 121.8 120 102 120Z"
          fill="currentColor"
        />
        <path
          d="M72 34.8001V16.8"
          stroke="#0D0D0D"
          strokeWidth="8"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M124.8 85.8C128.1 84.6 130.5 81.3 130.5 77.7V66.3C130.5 62.4 128.1 59.1 124.5 58.2"
          stroke="#0D0D0D"
          strokeWidth="8"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M19.5 86.1C15.9 84.9 13.2 81.6 13.2 77.7V66.3C13.2 62.4 15.6 59.4 18.9 58.2"
          stroke="#0D0D0D"
          strokeWidth="8"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M76.2 109.2H54C33.6 109.2 16.8 92.3999 16.8 71.9999C16.8 56.9999 25.5 44.0999 38.1 38.3999"
          stroke="#0D0D0D"
          strokeWidth="8"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M56.7 34.8H90C110.4 34.8 127.2 51.6 127.2 72C127.2 92.4 110.4 109.2 90 109.2"
          stroke="#0D0D0D"
          strokeWidth="8"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M102.6 57C106.8 60.6 109.8 66 109.8 72C109.8 82.8 101.1 91.5 90.3 91.5H53.7C42.9 91.5 34.2 82.8 34.2 72C34.2 61.2 42.9 52.5 53.7 52.5H90"
          stroke="#0D0D0D"
          strokeWidth="8"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M56.7 69.3V74.4"
          stroke="#0D0D0D"
          strokeWidth="8"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M86.7 69.3V74.4"
          stroke="#0D0D0D"
          strokeWidth="8"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </Box>
  );
}
