// components/ErrorBoundary.tsx
"use client";

import { ArrowIcon } from "@app/public/svgs";
import { Button, Center, Flex, Heading, Text, VStack } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { Component, ErrorInfo, ReactNode } from "react";

type ErrorBoundaryProps = {
  children: ReactNode;
};

type ErrorBoundaryState = {
  hasError: boolean;
  error: AppError | null;
};

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: AppError): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.log(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Center padding={5} height={"100%"}>
          <VStack spacing={5} p={8}>
            <Heading size="xl">¡Ups! Algo salió mal. Ya estamos trabajando en ello.</Heading>
            <Text color="gray.600">Estamos trabajando en ello por favor siga alguna de las siguientes acciones.</Text>
            <Flex align={"center"} mt={5}>
              <Button
                color={"black"}
                leftIcon={<ArrowIcon rotate={"180deg"} translateX={"180deg"} />}
                h={"fit-content"}
                py={2}
                onClick={() => window.history.back()}
              >
                Regresar
              </Button>
              <Button
                _hover={{ bg: "transparent" }}
                w={"fit-content"}
                height={"fit-content"}
                bg={"unset"}
                style={{ color: "gray", fontSize: 13, textDecoration: "underline" }}
                onClick={() => window.location.reload()}
              >
                Recargar la página
              </Button>
            </Flex>
          </VStack>
        </Center>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
