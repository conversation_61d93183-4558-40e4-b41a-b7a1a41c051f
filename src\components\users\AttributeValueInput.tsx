import { ChevronDownIcon } from "@chakra-ui/icons";
import { Button, Input, Menu, MenuButton, MenuItemOption, Menu<PERSON>ist, MenuOptionGroup } from "@chakra-ui/react";
import { attribute } from "@mainframe-peru/types";
import React from "react";

type AttributeValueMenuProps = Pick<attribute.ListAttributesResponse[number], "options" | "type"> & {
  onChange: (values: string[]) => void;
};

export function AttributeValueInput(props: AttributeValueMenuProps) {
  const [selection, setSelection] = React.useState<Set<string>>(new Set());
  const selectionArray = React.useMemo(() => {
    const a = Array.from(selection);
    return a;
  }, [selection]);

  React.useEffect(() => {
    props.onChange(selectionArray);
  }, [selection]);

  if (props.type === "TEXT") {
    return (
      <Input
        value={selectionArray[0] || ""}
        onChange={(e) => {
          setSelection(new Set([e.target.value]));
        }}
      />
    );
  }

  return (
    <Menu closeOnSelect={false}>
      <MenuButton bg="black" color="white" as={Button} rightIcon={<ChevronDownIcon color="white" />}>
        {selection.size ? selectionArray.join(", ") : "-"}
      </MenuButton>
      <MenuList>
        <MenuOptionGroup type="checkbox" value={selectionArray}>
          {props.options?.map((o) => (
            <MenuItemOption
              key={o.id}
              value={o.id}
              onClick={() => {
                if (selection.has(o.id)) {
                  selection.delete(o.id);
                } else {
                  if (props.type === "SINGLE") selection.clear();
                  selection.add(o.id);
                }
                setSelection(new Set(selection));
              }}
            >
              {o.value}
            </MenuItemOption>
          ))}
        </MenuOptionGroup>
      </MenuList>
    </Menu>
  );
}
