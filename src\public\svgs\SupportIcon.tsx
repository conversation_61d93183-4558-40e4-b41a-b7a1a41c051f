import { Box, BoxProps } from "@chakra-ui/react";

export function SupportIcon({ width = "24px", height = "24px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 20 20" fill="none">
        <path
          d="M15 0.43335H5C2 0.43335 0 2.43335 0 5.43335V11.4333C0 14.4333 2 16.4333 5 16.4333V18.5633C5 19.3633 5.89 19.8433 6.55 19.3933L11 16.4333H15C18 16.4333 20 14.4333 20 11.4333V5.43335C20 2.43335 18 0.43335 15 0.43335ZM10 12.6033C9.58 12.6033 9.25 12.2633 9.25 11.8533C9.25 11.4433 9.58 11.1033 10 11.1033C10.42 11.1033 10.75 11.4433 10.75 11.8533C10.75 12.2633 10.42 12.6033 10 12.6033ZM11.26 8.45335C10.87 8.71335 10.75 8.88335 10.75 9.16335V9.37335C10.75 9.78335 10.41 10.1233 10 10.1233C9.59 10.1233 9.25 9.78335 9.25 9.37335V9.16335C9.25 8.00335 10.1 7.43335 10.42 7.21335C10.79 6.96335 10.91 6.79335 10.91 6.53335C10.91 6.03335 10.5 5.62335 10 5.62335C9.5 5.62335 9.09 6.03335 9.09 6.53335C9.09 6.94335 8.75 7.28335 8.34 7.28335C7.93 7.28335 7.59 6.94335 7.59 6.53335C7.59 5.20335 8.67 4.12335 10 4.12335C11.33 4.12335 12.41 5.20335 12.41 6.53335C12.41 7.67335 11.57 8.24335 11.26 8.45335Z"
          fill="currentColor"
        />
      </svg>
    </Box>
  );
}
