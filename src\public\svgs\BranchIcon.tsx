import { Box, BoxProps } from "@chakra-ui/react";

export function BranchIcon({ width = "24px", height = "24px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none">
        <path
          d="M18.75 22C20.5449 22 22 20.5449 22 18.75C22 16.9551 20.5449 15.5 18.75 15.5C16.9551 15.5 15.5 16.9551 15.5 18.75C15.5 20.5449 16.9551 22 18.75 22Z"
          fill="currentColor"
        />
        <path
          d="M19 8C20.6569 8 22 6.65685 22 5C22 3.34315 20.6569 2 19 2C17.3431 2 16 3.34315 16 5C16 6.65685 17.3431 8 19 8Z"
          fill="currentColor"
        />
        <path
          d="M5 8C6.65685 8 8 6.65685 8 5C8 3.34315 6.65685 2 5 2C3.34315 2 2 3.34315 2 5C2 6.65685 3.34315 8 5 8Z"
          fill="currentColor"
        />
        <path
          d="M4.83 8.02C5.8 10.8 8.42 12.67 11.37 12.67C11.38 12.67 11.39 12.67 11.4 12.67L14.93 12.66C16.45 12.64 17.81 13.67 18.22 15.15V17C18.22 17.42 18.56 17.76 18.99 17.76C19.41 17.76 19.75 17.42 19.75 17V5.76C19.75 5.34 19.41 5 18.99 5C18.57 5 18.22 5.34 18.22 5.76V12.38C17.34 11.6 16.19 11.12 14.94 11.12C14.93 11.12 14.93 11.12 14.92 11.12L11.39 11.13C11.38 11.13 11.38 11.13 11.37 11.13C9.08 11.13 7.03 9.68 6.28 7.51C6.16 7.2 5.87 7 5.55 7C5.47 7 5.38 7.02 5.3 7.04C4.9 7.18 4.69 7.62 4.83 8.02Z"
          fill="currentColor"
        />
      </svg>
    </Box>
  );
}
