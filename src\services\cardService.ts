import { Card } from "@app/lib/card/type";
import { BaseService } from "@app/lib/service/baseService";

export class CardService extends BaseService {
  static async getUserCardsSsr(userId: string | number, token?: string): Promise<Card[]> {
    const { data } = await this.fetchData<{ cards: Card[] }>(`${this.BASE_URL}/card?userId=${userId}`, {
      headers: {
        Cookie: `session=${token}`,
      },
    });
    return data.cards;
  }

  static async getUserCards(userId: string | number, token?: string): Promise<Card[]> {
    const { data } = await this.fetchData<{ cards: Card[] }>(`${this.BASE_URL}/card?userId=${userId}`, {
      headers: {
        Cookie: `session=${token}`,
      },
    });
    return data.cards;
  }
}
