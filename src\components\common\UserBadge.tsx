"use client";

import { useSession } from "@app/hooks/useSession";
import { Box, Flex, Text, TextProps } from "@chakra-ui/react";

const firstLetterNameProps: TextProps = {
  alignItems: "center",
  justifyContent: "center",
  width: "35px",
  height: "35px",
  bg: "black",
  borderRadius: "100%",
  color: "green.300",
  fontWeight: "bold",
};

export function UserBadge() {
  const session = useSession();
  return (
    <Flex justifyContent={"flex-start"} alignItems={"center"} gap={"6px"}>
      <Flex {...firstLetterNameProps}>{session.admin?.firstName[0] || ""}</Flex>
      <Box lineHeight={"1"} textAlign={"left"}>
        <Text fontSize={"14px"} fontWeight={"bold"}>
          {`${session.admin?.firstName} ${session.admin?.lastName}`}
        </Text>
        <Text fontSize={"10px"} as={"small"}>
          Administrador
        </Text>
      </Box>
    </Flex>
  );
}
