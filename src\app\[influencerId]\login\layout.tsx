import { Flex, FlexProps } from "@chakra-ui/react";
import React from "react";

const containerProps: FlexProps = {
  as: "main",
  alignItems: "center",
  justifyContent: "center",
  minHeight: "100dvh",
  width: "100%",
  padding: "100px 0 50px 0",
};

export default async function LoginLayout({ children }: { children: React.ReactNode }) {
  return (
    <Flex {...containerProps} bg="black">
      {children}
    </Flex>
  );
}
