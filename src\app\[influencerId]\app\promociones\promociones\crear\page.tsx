import { promotions } from "@app/components/common/Breadcrumb";
import { PromotionForm } from "@app/components/promotion/PromotionForm";
import { PromotionFormProvider } from "@app/components/promotion/PromotionForm/PromotionFormProvider";
import { ContainerLayout } from "@app/layouts/ContainerLayout";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Crear promoción",
};

export default function CreatePromotionPage() {
  return (
    <ContainerLayout>
      <PageLayout
        breadcrumb={{ parent: promotions, name: "<PERSON>rea<PERSON>", url: "/promociones/promociones/crear" }}
        title="Crear promoción"
      >
        <PromotionFormProvider>
          <PromotionForm />
        </PromotionFormProvider>
      </PageLayout>
    </ContainerLayout>
  );
}
