import { AdminForm } from "@app/components/admin/AdminForm";
import { admins } from "@app/components/common/Breadcrumb";
import { ContainerLayout } from "@app/layouts/ContainerLayout";
import { PageLayout } from "@app/layouts/PageLayout";

export default function CreateAdmin() {
  return (
    <ContainerLayout>
      <PageLayout
        breadcrumb={{
          parent: admins,
          name: "<PERSON>rea<PERSON>",
          url: `/admins/crear`,
        }}
        title="Crear admin"
      >
        <AdminForm />
      </PageLayout>
    </ContainerLayout>
  );
}
