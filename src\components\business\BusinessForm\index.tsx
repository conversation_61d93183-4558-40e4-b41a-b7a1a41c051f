"use client";

import { Button } from "@app/components/common/Button";
import { ImageDropzone } from "@app/components/common/ImageDropzone";
import { Flex, FormControl, FormLabel, Heading, Input, Skeleton, Stack, Text, Textarea } from "@chakra-ui/react";
import { useBusinessForm } from "@app/hooks/useBusinessForm";
import { PromotionsTable } from "@app/components/promotion/PromotionTable";
import { PromotionPageProvider } from "@app/contexts/PromotionPageProvider";
import { PromotionToolbar } from "@app/components/promotion/PromotionToolbar";
import { useSearchParams } from "next/navigation";

export function BusinessForm({ isEditing }: { isEditing?: boolean }) {
  const { business, isLoading, loadSkeleton, onDrop, onSubmitBusinessForm, onChangeBusiness } = useBusinessForm();
  const id = useSearchParams().get("id");

  return (
    <Stack spacing={5}>
      <Stack as="form" gap={5} onSubmit={onSubmitBusinessForm} bg="black" color="white" borderRadius={21} p={5}>
        <Flex alignItems={"center"} justify={"space-between"}>
          <Heading size={"md"} color={"#5C73F2"}>
            Datos del Negocio
          </Heading>
        </Flex>
        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>Nombre</FormLabel>
            <Input
              value={business.name}
              onChange={(e) => onChangeBusiness({ name: e.target.value })}
              placeholder="Ingresa el nombre del negocio"
              bgColor={"gray.500"}
            />
          </FormControl>
        </Skeleton>

        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>RUC</FormLabel>
            <Input
              value={business.ruc}
              onChange={(e) => onChangeBusiness({ ruc: e.target.value })}
              placeholder="Ingresa el RUC del negocio"
              bgColor={"gray.500"}
            />
          </FormControl>
        </Skeleton>

        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>Descripción</FormLabel>
            <Textarea
              value={business.description}
              onChange={(e) => onChangeBusiness({ description: e.target.value })}
              placeholder="Ingresa la descripción del negocio"
              resize="none"
              rows={5}
              bgColor={"gray.500"}
            />
          </FormControl>
        </Skeleton>

        <Skeleton borderRadius={21} isLoaded={!loadSkeleton}>
          <FormControl>
            <FormLabel fontSize={13}>Logo</FormLabel>
            <ImageDropzone onDrop={onDrop} previewUrl={business.imageUrl} />
            <Text fontSize={13} color="gray.500" mt={2}>
              Tamaño máximo: 2MB
            </Text>
          </FormControl>
        </Skeleton>

        <Flex justifyContent={"center"}>
          <Button type="submit" isLoading={isLoading} width={{ base: "100%", md: "40%", lg: "25%" }}>
            {isEditing ? "Actualizar" : "Crear"} negocio
          </Button>
        </Flex>
      </Stack>

      {id && (
        <Stack bg="black" color="white" borderRadius={21} p={5}>
          <PromotionPageProvider businessId={Number(id)}>
            <PromotionToolbar />
            <PromotionsTable />
          </PromotionPageProvider>
        </Stack>
      )}
    </Stack>
  );
}
