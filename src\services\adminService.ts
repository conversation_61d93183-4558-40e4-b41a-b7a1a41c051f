import { BaseService } from "@app/lib/service/baseService";
import { admin } from "@mainframe-peru/types";

export class AdminService extends BaseService {
  static async getAdmins(query: Partial<admin.ListAdminsRequest>): Promise<admin.ListAdminsResponse> {
    const url = `${this.BASE_URL}/admin/list-admins?${this.buildQueryParams(query)}`;
    const { data } = await this.fetchData<admin.ListAdminsResponse>(url);
    return data;
  }
  static async getOne(query: admin.GetAdminRequest): Promise<admin.GetAdminResponse> {
    const url = `${this.BASE_URL}/admin?${this.buildQueryParams(query)}`;
    const { data } = await this.fetchData<admin.GetAdminResponse>(url);
    return data;
  }
  static async create(payload: admin.CreateAdminRequest) {
    const response = await this.fetchData(`${this.BASE_URL}/admin`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    return response;
  }
  static async update(payload: admin.UpdateAdminRequest) {
    const response = await this.fetchData(`${this.BASE_URL}/admin`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    return response;
  }
  static async delete(id: number) {
    const query: admin.DeleteAdminRequest = { id };
    const response = await this.fetchData(`${this.BASE_URL}/admin?${this.buildQueryParams(query)}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response;
  }
}
