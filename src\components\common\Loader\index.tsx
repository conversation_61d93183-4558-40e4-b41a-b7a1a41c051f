import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ack } from "@chakra-ui/react";

import { motion } from "framer-motion";

export const Loader = () => {
  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
      <Flex align="center" justify="center" w="100%" bgColor="#1F1F1F" h="100%" position="fixed" top={0} left={0}>
        <Stack gap={5} align="center" justify="center">
          <Spinner size="xl" color="green.300" />
          <Heading size="sm">Espera un momento...</Heading>
        </Stack>
      </Flex>
    </motion.div>
  );
};
