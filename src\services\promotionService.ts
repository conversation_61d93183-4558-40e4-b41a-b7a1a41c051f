import { BaseService } from "@app/lib/service/baseService";
import { businessPromotion } from "@mainframe-peru/types";

export class PromotionService extends BaseService {
  static async getPromotions(
    query: Partial<businessPromotion.ListBusinessPromotionRequest>,
  ): Promise<businessPromotion.ListBusinessPromotionResponse> {
    const url = `${this.BASE_URL}/business-promotion/list?${this.buildQueryParams(query)}`;
    console.log(url);
    const { data } = await this.fetchData<businessPromotion.ListBusinessPromotionResponse>(url, {
      credentials: "include",
    });
    return data;
  }

  static async createPromotion(
    payload: businessPromotion.CreateBusinessPromotionRequest,
  ): Promise<businessPromotion.CreateBusinessPromotionResponse> {
    const { data } = await this.fetchData<businessPromotion.CreateBusinessPromotionResponse>(
      `${this.BASE_URL}/business-promotion/`,
      {
        method: "POST",
        credentials: "include",
        body: JSON.stringify(payload),
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
    return data;
  }

  static async getPromotion(id: string) {
    const { data } = await this.fetchData<businessPromotion.GetBusinessPromotionResponse>(
      `${this.BASE_URL}/business-promotion?${this.buildQueryParams({ id })}`,
      {
        method: "GET",
        credentials: "include",
      },
    );
    return data;
  }

  static async updatePromotion(payload: businessPromotion.UpdateBusinessPromotionRequest) {
    const { data } = await this.fetchData<businessPromotion.UpdateBusinessPromotionResponse>(
      `${this.BASE_URL}/business-promotion`,
      {
        method: "PUT",
        credentials: "include",
        body: JSON.stringify(payload),
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
    return data;
  }

  static async invalidateCache() {
    await this.fetchData(`${this.BASE_URL}/business-promotion/invalidate-cache`, {
      method: "POST",
    });
  }
}
