/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "export",
  trailingSlash: true,
  experimental: {
    optimizePackageImports: ["@public/svgs"],
  },
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "core-backend-s3publicinfluencerassetsbucket-23crkztyemea.s3.us-east-1.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "core-backend-s3publicinfluencerassetsbucket-cesmyiihtt1r.s3.us-east-1.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "core-backend-s3publicinfluencerassetsbucket-uiedm6s7klcu.s3.us-east-1.amazonaws.com",
      },
    ],
  },
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: "https://api.dev.pchujoy.app/api/:path*",
      },
      {
        source: "/api/activity-log/:path*",
        destination: "https://api.dev.pchujoy.app/api/activity-log/:path*",
      },
    ];
  },
};

export default nextConfig;
