import { Box, BoxProps } from "@chakra-ui/react";

export function CalendarIcon({ width = "24px", height = "24px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 16 16" fill="none">
        <path d="M5 1L5 3" stroke="currentColor" strokeLinecap="round" />
        <path d="M8 1L8 3" stroke="currentColor" strokeLinecap="round" />
        <path d="M11 1L11 3" stroke="currrentColor" strokeLinecap="round" />
        <rect x="2" y="2" width="12" height="12" rx="2" stroke="currentColor" />
        <path d="M4.5 4.5H11.5" stroke="currentColor" strokeLinecap="round" />
        <circle cx="6" cy="7" r="1" stroke="currentColor" />
        <circle cx="6" cy="11" r="1" stroke="currentColor" />
        <circle cx="10" cy="7" r="1" stroke="currentColor" />
        <circle cx="10" cy="11" r="1" stroke="currentColor" />
      </svg>
    </Box>
  );
}
