"use client";

import { UserService } from "@app/services/userService";
import { ChevronDownIcon, DownloadIcon } from "@chakra-ui/icons";
import {
  Box,
  Button as CButton,
  Menu,
  MenuButton,
  MenuItemOption,
  MenuList,
  MenuOptionGroup,
  Stack,
  useToast,
} from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import React from "react";
import { Button } from "../common/Button";
import { Datepicker } from "../common/Datepicker";
import { Modal } from "../common/Modal";

export function DownloadUsersList() {
  const [userStatus, setUserStatus] = React.useState<"ALL" | "SUB" | "NOT_SUB">("ALL");
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [isOpen, setIsOpen] = React.useState<boolean>(false);
  const [dateStart, setDateStart] = React.useState<Date | undefined>(() => {
    const today = new Date();
    today.setFullYear(today.getFullYear() - 1);
    return today;
  });
  const [dateEnd, setDateEnd] = React.useState<Date | undefined>(new Date());
  const toast = useToast();

  const handleDownloadList = async () => {
    setIsLoading(true);
    try {
      await UserService.getUsersCsv({
        detailed: true,
        hasRecurrence: userStatus === "ALL" ? undefined : userStatus === "SUB",
        creationStart: dateStart,
        creationEnd: dateEnd,
        limit: -1,
      });
      toast({
        status: "success",
        title: "Download successfully",
        description: "Su descarga se ha completado.",
      });
    } catch (error) {
      if (error instanceof AppError) {
        const { code, message } = error;
        toast({
          status: "error",
          title: code,
          description: message,
        });
      }
    }
    setIsLoading(false);
  };

  let selectedUserStatus = "Todos";
  if (userStatus === "SUB") {
    selectedUserStatus = "Suscritos";
  } else if (userStatus === "NOT_SUB") {
    selectedUserStatus = "No suscritos";
  }

  return (
    <>
      <Modal
        modalProps={{ isOpen, onClose: () => setIsOpen(false) }}
        title="Descarga de usuarios"
        footerContent={
          <>
            <Button variant="ghost" onClick={() => setIsOpen(false)} mr={3}>
              Close
            </Button>
            <Button
              onClick={handleDownloadList}
              bgColor={"black"}
              color={"white"}
              _hover={{ color: "black", bgColor: "green.300" }}
            >
              Descargar
            </Button>
          </>
        }
      >
        <Stack gap={2} mb={3}>
          <Box>Status</Box>
          <Menu>
            <MenuButton as={CButton} rightIcon={<ChevronDownIcon />}>
              {selectedUserStatus}
            </MenuButton>
            <MenuList>
              <MenuOptionGroup type="radio" defaultValue={userStatus} onChange={(d) => setUserStatus(d as "ALL")}>
                <MenuItemOption value="ALL">Todos</MenuItemOption>
                <MenuItemOption value="SUB">Suscritos</MenuItemOption>
                <MenuItemOption value="NOT_SUB">No suscritos</MenuItemOption>
              </MenuOptionGroup>
            </MenuList>
          </Menu>
        </Stack>
        <Stack mb={3}>
          <Box>Usuarios creados desde</Box>
          <Datepicker date={dateStart || new Date()} type="date" onChange={(d) => setDateStart(d)} />
        </Stack>
        <Stack mb={3}>
          <Box>Usuarios creados hasta</Box>
          <Datepicker date={dateEnd || new Date()} type="date" onChange={(d) => setDateEnd(d)} />
        </Stack>
      </Modal>
      <Button
        isLoading={isLoading}
        onClick={() => setIsOpen(true)}
        bgColor={"black"}
        color={"white"}
        _hover={{ color: "black", bgColor: "green.300" }}
        leftIcon={<DownloadIcon />}
        fontSize={13}
        minW={{ base: "100%", md: "fit-content" }}
      >
        Descargar usuarios
      </Button>
    </>
  );
}
