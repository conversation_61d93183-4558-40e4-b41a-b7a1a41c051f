import { lottery } from "@app/components/common/Breadcrumb";
import { EventsTable } from "@app/components/event/EventsTable";
import { EventsToolbar } from "@app/components/event/EventToolbar";
import { EventPageProvider } from "@app/contexts/EventPageProvider";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Eventos y Premios",
  description: "Gestiona tus eventos y premios para tus suscriptores",
};

export default function LotteryEventsPage() {
  return (
    <PageLayout breadcrumb={lottery} title="Eventos y premios">
      <EventPageProvider>
        <EventsToolbar />
        <EventsTable />
      </EventPageProvider>
    </PageLayout>
  );
}
