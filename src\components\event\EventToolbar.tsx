"use client";

import { useEventsPage } from "@app/contexts/EventPageProvider";
import { useSession } from "@app/hooks/useSession";
import { EventService } from "@app/services/eventService";
import { Flex, Stack, Text, useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { common } from "@mainframe-peru/types";
import Link from "next/link";
import React from "react";
import { Button } from "../common/Button";
import { CheckButton } from "../common/CheckButton";
import { Search } from "../common/Search";
import { Option, Select } from "../common/Select";

const options: Option[] = [
  { value: "", text: "Todos" },
  { value: "PRIZE", text: "Premio" },
  { value: "PRODUCT", text: "Producto" },
  { value: "IN-PERSON", text: "Reuniones" },
  { value: "GAME", text: "Videojuegos" },
];

export function EventsToolbar() {
  const { filters, onSetEventStatus, onSetEventType, onSetEvents, onSetIsLoading } = useEventsPage();
  const session = useSession();
  const inputRef = React.useRef<HTMLInputElement | null>(null);
  const toast = useToast();

  const [loadingInvalidate, setLoadingInvalidate] = React.useState(false);

  const onInvalidateClick = async () => {
    setLoadingInvalidate(true);
    await EventService.invalidateCache();
    setLoadingInvalidate(false);
  };

  const onTogglerStatus = (status: common.EventStatus) => {
    if (filters.status === status) {
      onSetEventStatus(undefined);
      return;
    }
    onSetEventStatus(status);
  };

  const onTogglerType = (type: string | string[]) => onSetEventType(type as common.EventType);

  const onSearch = (value: string) => {
    onSetIsLoading(true);
    EventService.getEvents({
      influencerId: session.influencer.id,
      compound: value,
    })
      .then((events) => {
        onSetEvents(events);
        onSetIsLoading(false);
      })
      .catch((error) => {
        onSetIsLoading(false);
        if (error instanceof AppError) {
          toast({
            status: "error",
            description: error.message,
            title: error.code,
          });
        }
      });
  };
  const onClick = () => {
    if (inputRef.current && inputRef.current.value.length === 0) {
      return;
    }
    onSearch(inputRef.current?.value || "");
  };

  return (
    <Stack gap={34}>
      <Flex gap={5}>
        <Search
          ref={inputRef}
          onSearch={onSearch}
          onEmpty={() => onSearch("")}
          flexProps={{ flexBasis: "100%", width: { base: "100%", md: "auto" } }}
          placeholder="Buscar evento por nombre..."
        />
        <Button
          w={{ base: "100%", md: "auto" }}
          color={"white"}
          fontSize={13}
          bg={"black"}
          _hover={{ bg: "black" }}
          onClick={onClick}
        >
          Buscar
        </Button>
      </Flex>
      <Flex justifyContent={"space-between"} flexWrap={{ base: "wrap", md: "nowrap" }} gap={34}>
        <Flex gap={5} align={"center"}>
          <CheckButton
            isActive={filters.status === "ACTIVE"}
            text="Activos"
            onClick={() => onTogglerStatus("ACTIVE")}
          />
          <CheckButton
            isActive={filters.status === "INACTIVE"}
            text="Inactivos"
            onClick={() => onTogglerStatus("INACTIVE")}
          />
        </Flex>
        <Flex gap={5} flexWrap={{ base: "wrap", md: "nowrap" }} justifyContent={"space-between"}>
          <Flex align={"center"} gap={3}>
            <Text as={"small"}>Tipo</Text>
            <Select
              options={options}
              value={filters.type || ""}
              onChange={onTogglerType}
              selectButtonProps={{ minW: 150 }}
            />
          </Flex>
          {session.isInPolicy("event", "UPDATE_EVENT") && (
            <Button
              variant="outline"
              mr={2}
              isLoading={loadingInvalidate}
              onClick={onInvalidateClick}
              mb={[3, null, 0]}
            >
              Refrescar contenido
            </Button>
          )}
          {session.isInPolicy("event", "CREATE_EVENT") && (
            <Link href={`/${session.admin?.influencerId}/app/premios-y-eventos/crear`}>
              <Button fontSize={13} w={{ base: "100%", md: "auto" }}>
                Crear evento
              </Button>
            </Link>
          )}
        </Flex>
      </Flex>
    </Stack>
  );
}
