import { Button as ChakraButton, ButtonProps as ChakraButtonProps, Flex } from "@chakra-ui/react";
import React from "react";

export type ButtonProps = {
  withIcon?: React.ReactNode;
} & ChakraButtonProps;

export function Button({ children, ...props }: ButtonProps) {
  return (
    <ChakraButton {...props}>
      <Flex justifyContent={"center"} alignItems={"center"} gap={"8px"}>
        {children}
      </Flex>
    </ChakraButton>
  );
}
