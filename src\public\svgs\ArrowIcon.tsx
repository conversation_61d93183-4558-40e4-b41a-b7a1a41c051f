import { Box, BoxProps } from "@chakra-ui/react";

export function ArrowIcon({ width = "24px", height = "24px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 25 24" fill="currentColor">
        <path
          d="M16.69 2H8.31C4.67 2 2.5 4.17 2.5 7.81V16.18C2.5 19.83 4.67 22 8.31 22H16.68C20.32 22 22.49 19.83 22.49 16.19V7.81C22.5 4.17 20.33 2 16.69 2ZM19.03 12.53L14.74 16.82C14.59 16.97 14.4 17.04 14.21 17.04C14.02 17.04 13.83 16.97 13.68 16.82C13.39 16.53 13.39 16.05 13.68 15.76L16.69 12.75H6.5C6.09 12.75 5.75 12.41 5.75 12C5.75 11.59 6.09 11.25 6.5 11.25H16.69L13.68 8.24C13.39 7.95 13.39 7.47 13.68 7.18C13.97 6.89 14.45 6.89 14.74 7.18L19.03 11.47C19.17 11.61 19.25 11.8 19.25 12C19.25 12.2 19.17 12.39 19.03 12.53Z"
          fill="currentColor"
        />
      </svg>
    </Box>
  );
}
