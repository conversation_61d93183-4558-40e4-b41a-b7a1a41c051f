version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20

    commands:
      - npm config set @mainframe-peru:registry https://npm.pkg.github.com
      - npm config set //npm.pkg.github.com/:_authToken $GITHUB_TOKEN
      - npm ci

  build:
    commands:
      - echo NEXT_PUBLIC_API_BASE_URL=https://$APP_DOMAIN/api > .env
      - npm run build
      - aws s3 sync ./out s3://${DEPLOY_BUCKET} --delete
      - aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION --paths '/*'
