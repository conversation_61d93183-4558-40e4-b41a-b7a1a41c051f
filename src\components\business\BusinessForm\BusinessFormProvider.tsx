"use client";

import React, { createContext, useState } from "react";
import { business } from "@mainframe-peru/types";
import { useRouter, useSearchParams } from "next/navigation";
import { BusinessService } from "@app/services/businessService";
import { AppError } from "@mainframe-peru/common-core";
import { useToast } from "@app/hooks/useToast";
import { useSession } from "@app/hooks/useSession";

export const defaultDate = new Date();
export type Business = business.CreateBusinessRequest & { file?: File; imageUrl?: string };

interface BusinessFormContextProps {
  business: Partial<Business>;
  isLoading: boolean;
  loadSkeleton: boolean;
  onChangeBusiness: (values: Partial<Business>) => void;
  onDrop: (files: File[]) => void;
  onSubmitBusinessForm: (e: React.FormEvent<HTMLDivElement>) => void;
}

export const BusinessFormContext = createContext<BusinessFormContextProps>({} as BusinessFormContextProps);

export function BusinessFormProvider({ children }: { children: React.ReactNode }) {
  const id = useSearchParams().get("id");
  const [business, setBusiness] = useState<Business>({
    name: "",
    description: "",
    file: undefined,
    imageUrl: undefined,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [loadSkeleton, setLoadSkeleton] = useState(true);
  const { fire } = useToast();
  const router = useRouter();
  const { influencer } = useSession();

  const onChangeBusiness = (values: Partial<Business>) => {
    setBusiness((prev) => ({ ...prev, ...values }));
  };

  const onDrop = React.useCallback(
    (acceptedFiles: File[]) => {
      const reader = new FileReader();
      const file = acceptedFiles[0];
      reader.onload = () => {
        const imageUrl = reader.result as string;
        // 2MB limit 2000000
        if (file.size > 2000000) {
          fire("Error de formulario", "El peso de la imagen excede al máximo recomendado 2MB", "error");
          return;
        }
        setBusiness((prevBusiness) => ({
          ...prevBusiness,
          file,
          imageUrl,
        }));
      };
      reader.readAsDataURL(acceptedFiles[0]);
    },
    [fire],
  );

  const onSubmitBusinessForm = async (e: React.FormEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsLoading(true);

    const { description, name, file, ruc, imageUrl } = business;

    if ([!description, !name, !file && !imageUrl, !ruc].includes(true)) {
      fire("Faltan datos", "Se debe completar todos los campos", "error");
      setIsLoading(false);
      return;
    }

    try {
      if (!id) {
        const formData = BusinessService.createFormData({
          name,
          description,
          file,
          ruc,
        });

        await BusinessService.createBusiness(formData);
        fire("¡Éxito!", "El negocio se creó correctamente.", "success");
        router.push(`/${influencer.id}/app/promociones/negocios`);
      } else {
        const formData = BusinessService.createFormData({
          id,
          name,
          description,
          file,
          ruc,
        });
        await BusinessService.updateBusiness(formData);
        fire("¡Éxito!", "El negocio se actualizó correctamente.", "success");
      }
    } catch (error) {
      if (error instanceof AppError) {
        fire(error.code, error.message, "error");
      }
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    if (!id) {
      setLoadSkeleton(false);
      return;
    }

    const fetchEvent = async () => {
      try {
        const business = await BusinessService.getBusiness(id);
        const { description, imageUrl, name, ruc } = business;
        setBusiness({
          description,
          imageUrl,
          name,
          ruc,
        });
        setLoadSkeleton(false);
      } catch (error) {
        if (error instanceof AppError) {
          fire(error.code, error.message, "error");
          setLoadSkeleton(false);
        }
      }
    };
    fetchEvent();
  }, [id, fire]);

  return (
    <BusinessFormContext.Provider
      value={{
        business,
        isLoading,
        loadSkeleton,
        onChangeBusiness,
        onDrop,
        onSubmitBusinessForm,
      }}
    >
      {children}
    </BusinessFormContext.Provider>
  );
}
