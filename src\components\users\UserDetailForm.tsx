"use client";

import { useSession } from "@app/hooks/useSession";
import { countries, EMAIL_REGEX_PATTERN } from "@app/lib/common/constants";
import { UserService } from "@app/services/userService";
import {
  Flex,
  FormControl,
  FormControlProps,
  FormLabel,
  Heading,
  Input,
  Select,
  Skeleton,
  Stack,
} from "@chakra-ui/react";
import { CountryAlpha2 } from "@mainframe-peru/types/build/common";
import React from "react";
import { Controller, useForm } from "react-hook-form";
import { Button } from "../common/Button";
import { Datepicker } from "../common/Datepicker";
import { useUserDetailsPage } from "./UserDetailProvider";
import { useSearchParams } from "next/navigation";
import { useToast } from "@app/hooks/useToast";
import { AppError } from "@mainframe-peru/common-core";

const formControlProps: FormControlProps = {
  maxW: {
    base: "100%",
    xl: "calc(33.33% - 1rem)",
  },
};

type FormValues = {
  birthDate: Date | undefined;
  gender: "F" | "M" | "NB" | "NO";
  phone: string;
  line1: string;
  country: CountryAlpha2 | undefined | "";
  email: string;
  documentValue: string;
  firstName: string;
  lastName: string;
};

const genders = [
  { text: "Femenino", value: "F" },
  { text: "Masculino", value: "M" },
  { text: "No binario", value: "NB" },
  { text: "Sin definir", value: "NO" },
];

export function PersonalDetailForm() {
  const [loading, setLoading] = React.useState<boolean>(false);
  const { user, isLoading } = useUserDetailsPage();
  const toast = useToast();
  const session = useSession();
  const userId = Number(useSearchParams().get("id"));
  const { register, handleSubmit, reset, control } = useForm<FormValues>({
    defaultValues: {
      birthDate: undefined,
      gender: "NO",
      phone: "",
      line1: "",
      country: undefined,
      email: "",
      documentValue: "",
      firstName: "",
      lastName: "",
    },
  });

  React.useEffect(() => {
    if (user) {
      reset({
        birthDate: user?.birthDate ? new Date(user.birthDate) : undefined,
        gender: user?.gender || "NO",
        phone: user?.phone || "",
        line1: user?.line1 || "",
        country: user?.country || undefined,
        email: user?.email || "",
        documentValue: user?.documentValue || "",
        firstName: user?.firstName || "",
        lastName: user?.lastName || "",
      });
    }
  }, [user]);

  const onSubmit = async (values: FormValues) => {
    try {
      setLoading(true);
      await UserService.updateUser({
        influencerId: session.admin?.influencerId,
        userId,
        ...values,
        country: values.country == "" ? undefined : values.country,
      });
      setLoading(false);
      toast.fire("Usuario actualizado", "El usuario ha sido actualizado", "success");
    } catch (error) {
      if (error instanceof AppError) {
        toast.fire(error.code, error.message, "error");
        setLoading(false);
      }
    }
  };

  return (
    <Skeleton isLoaded={!isLoading} borderRadius={21}>
      <Stack bg="black" color="white" borderRadius={21} p={5} as="form" spacing={5} onSubmit={handleSubmit(onSubmit)}>
        <Flex alignItems={"center"} justify={"space-between"}>
          <Heading size={"md"} color={"blue.500"}>
            Datos Personales
          </Heading>
        </Flex>
        <Stack spacing={5}>
          <Flex flexWrap={"wrap"} justify={"space-between"} gap={5}>
            <FormControl {...formControlProps}>
              <FormLabel fontSize={13}>Correo</FormLabel>
              <Input {...register("email", { pattern: EMAIL_REGEX_PATTERN })} bgColor={"gray.400"} />
            </FormControl>
            <FormControl {...formControlProps}>
              <FormLabel fontSize={13}>Nombres</FormLabel>
              <Input {...register("firstName")} bgColor={"gray.400"} />
            </FormControl>
            <FormControl {...formControlProps}>
              <FormLabel fontSize={13}>Apellidos</FormLabel>
              <Input {...register("lastName")} bgColor={"gray.400"} />
            </FormControl>
          </Flex>
          <Flex flexWrap={"wrap"} justify={"space-between"} gap={5}>
            <FormControl {...formControlProps}>
              <FormLabel fontSize={13}>Género</FormLabel>
              <Select bg={"gray.400"} color={"white"} {...register("gender")} borderRadius={13} fontSize={13}>
                {genders.map((g) => (
                  <option key={g.value} value={g.value} style={{ backgroundColor: "black" }}>
                    {g.text}
                  </option>
                ))}
              </Select>
            </FormControl>
            <FormControl {...formControlProps}>
              <FormLabel fontSize={13}>Teléfono</FormLabel>
              <Input {...register("phone")} bgColor={"gray.400"} />
            </FormControl>
            <FormControl {...formControlProps}>
              <FormLabel fontSize={13}>Documento</FormLabel>
              <Input {...register("documentValue")} bgColor={"gray.400"} />
            </FormControl>
          </Flex>
          <Flex flexWrap={"wrap"} justify={"space-between"} gap={5}>
            <FormControl {...formControlProps}>
              <FormLabel fontSize={13}>Fecha de nacimiento</FormLabel>
              <Controller
                name="birthDate"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <Datepicker
                    type="date"
                    date={value ?? new Date()}
                    onChange={onChange}
                    styles={{
                      bgColor: "gray.400",
                    }}
                  />
                )}
              />
            </FormControl>
            <FormControl {...formControlProps}>
              <FormLabel fontSize={13}>Dirección</FormLabel>
              <Input {...register("line1")} bgColor={"gray.400"} />
            </FormControl>
            <FormControl {...formControlProps}>
              <FormLabel fontSize={13}>País</FormLabel>
              <Select bg={"gray.400"} color={"white"} {...register("country")} borderRadius={13} fontSize={13}>
                {countries.map((c) => (
                  <option key={c.value} style={{ backgroundColor: "black" }} value={c.value}>
                    {c.text}
                  </option>
                ))}
              </Select>
            </FormControl>
          </Flex>
        </Stack>
        {session.isInPolicy("user", "PUT_USER") && (
          <Flex justify="flex-end" gap={5}>
            <Button type="submit" isLoading={loading}>
              Actualizar
            </Button>
          </Flex>
        )}
      </Stack>
    </Skeleton>
  );
}
