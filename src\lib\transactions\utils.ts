import { Transaction } from "./types";

export function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function formatTime(incomingDate: Date) {
  const date = new Date(incomingDate);
  const [fullDate, hour] = date
    .toLocaleDateString("es-PE", {
      hour: "2-digit",
      minute: "2-digit",
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour12: true,
      timeZone: "America/Lima",
    })
    .split(", ");

  return {
    dateObject: date,
    dateToString: fullDate,
    hour,
  };
}

export function formatDate(date: Date): Date {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}` as unknown as Date;
}

export function getCurrencyFormat(currency: string, amount: string) {
  const currencySymbol = currency === "PEN" ? "S/." : "$";
  return `Total: ${currencySymbol} ${Number(amount).toFixed(2)}`;
}

export function filterTransactions(transactions: Transaction[], status: string) {
  return transactions.filter((transaction) => transaction.state === status);
}

export function limitTransactions(transactions: Transaction[], limit: number) {
  return transactions.slice(0, limit + 1);
}
