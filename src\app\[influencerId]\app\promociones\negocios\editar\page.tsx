import { BusinessForm } from "@app/components/business/BusinessForm";
import { BusinessFormProvider } from "@app/components/business/BusinessForm/BusinessFormProvider";
import { business } from "@app/components/common/Breadcrumb";
import { ContainerLayout } from "@app/layouts/ContainerLayout";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Editar negocio",
};
export default function EditBusinessPage() {
  return (
    <ContainerLayout>
      <PageLayout
        breadcrumb={{ parent: business, name: "Editar", url: "/promociones/negocios/editar" }}
        title="Editar Negocio"
      >
        <BusinessFormProvider>
          <BusinessForm isEditing />
        </BusinessFormProvider>
      </PageLayout>
    </ContainerLayout>
  );
}
