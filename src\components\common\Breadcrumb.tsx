"use client";

import { useSession } from "@app/hooks/useSession";
import {
  Breadcrumb as ChakraBreadcrumb,
  BreadcrumbItem as ChakraBreadcrumbItem,
  BreadcrumbLink as ChakraBreadcrumbLink,
} from "@chakra-ui/react";

export type BreadCrumbItem = {
  name: string;
  url: string;
  parent: BreadCrumbItem | undefined;
};

export const dashboard: BreadCrumbItem = {
  name: "Inicio",
  url: "/dashboard",
  parent: undefined,
};

export const metricasTransacciones: BreadCrumbItem = {
  name: "Transacciones",
  url: "/dashboard",
  parent: dashboard,
};

export const pagos: BreadCrumbItem = {
  name: "Pagos",
  url: "/pagos",
  parent: dashboard,
};

export const suscriptores: BreadCrumbItem = {
  name: "Usuarios",
  url: "/usuarios",
  parent: dashboard,
};

export const admins: BreadCrumbItem = {
  name: "Admins",
  url: "/admins",
  parent: dashboard,
};

export const complaints: BreadCrumbItem = {
  name: "Quejas y Reclamos",
  url: "/complaints",
  parent: dashboard,
};

export const webhooks: BreadCrumbItem = {
  name: "Webhooks",
  url: "/webhooks",
  parent: undefined,
};

export const configuracion: BreadCrumbItem = {
  name: "Configuracion",
  url: "/config",
  parent: dashboard,
};

export const lottery: BreadCrumbItem = {
  parent: dashboard,
  name: "Eventos y Premios",
  url: "/premios-y-eventos",
};

export const business: BreadCrumbItem = {
  parent: dashboard,
  name: "Negocios",
  url: "/promociones/negocios",
};

export const promotions: BreadCrumbItem = {
  parent: dashboard,
  name: "Promociones",
  url: "/promociones/promociones",
};

export function Breadcrumb({ breadCrumbItem }: { breadCrumbItem: BreadCrumbItem }) {
  const breadCrumbPath: BreadCrumbItem[] = [];

  // Building recursive path
  function buildBreadCrumbPath(currentItem: BreadCrumbItem) {
    if (currentItem.parent === undefined) {
      breadCrumbPath.push(currentItem);
      return;
    }
    buildBreadCrumbPath(currentItem.parent);
    breadCrumbPath.push(currentItem);
  }

  function getBreadCrumbPath(): BreadCrumbItem[] {
    buildBreadCrumbPath(breadCrumbItem);
    return breadCrumbPath;
  }

  const session = useSession();

  return (
    <ChakraBreadcrumb separator=">" mb={21} textColor={"gray.200"}>
      {getBreadCrumbPath().map((element) => {
        return (
          <ChakraBreadcrumbItem key={element.name} isCurrentPage={element.name === breadCrumbItem.name}>
            <ChakraBreadcrumbLink href={`/${session.admin?.influencerId}/app` + element.url} fontSize={"13px"}>
              {element.name}
            </ChakraBreadcrumbLink>
          </ChakraBreadcrumbItem>
        );
      })}
    </ChakraBreadcrumb>
  );
}
