"use client";

import { Box } from "@chakra-ui/react";
import { business } from "@mainframe-peru/types";
import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { format } from "date-fns";
import { Table } from "../common/Table";
import { useBusinessPage } from "@app/contexts/BusinessPageProvider";
import { useSession } from "@app/hooks/useSession";
import Link from "next/link";
import { EditIcon } from "@chakra-ui/icons";

export function BusinessesTable() {
  const { businesses, isLoading } = useBusinessPage();

  const makeRows = (businesses: business.GetBusinessResponse[]) => {
    return businesses.map((business) => {
      const { id, description, name, createdAt } = business;
      const createdDateFormated = createdAt ? format(new Date(createdAt), TABLE_STANDARD_FORMAT_DATE) : "";

      return [
        id,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {name}
        </Box>,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {description}
        </Box>,
        createdDateFormated,
        <EditRow key={id} id={id} />,
      ];
    });
  };
  return (
    <Table
      isLoading={isLoading}
      rows={makeRows(businesses)}
      boxProps={{ mt: 34 }}
      headers={["Id", "Nombre", "Descripción", "Fecha de creación", ""]}
    />
  );
}

function EditRow({ id }: { id: number }) {
  const session = useSession();
  return (
    <Link href={`/${session.admin?.influencerId}/app/promociones/negocios/editar/?id=${id}`}>
      <EditIcon />
    </Link>
  );
}
