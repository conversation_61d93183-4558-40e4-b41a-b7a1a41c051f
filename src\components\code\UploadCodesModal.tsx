import { Flex, Stack, Text, Textarea } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { useRouter } from "next/navigation";
import React from "react";
import { Button } from "../common/Button";
import { Modal } from "../common/Modal";
import { CodeService } from "@app/services/codeService";
import { useToast } from "@app/hooks/useToast";

type DeleteModalProps = {
  promotionId: number;
  isOpen: boolean;
  onClose: () => void;
  onOpen?: () => void;
};

export function UploadCodesModal({ isOpen, onClose, promotionId }: DeleteModalProps) {
  const [isLoading, setIsLoading] = React.useState(false);
  const [idArea, setIdArea] = React.useState<string>("");
  const router = useRouter();
  const { fire } = useToast();

  const onUploadCodes = async () => {
    setIsLoading(true);
    try {
      if (!idArea.trim()) {
        fire("Faltan datos", "Códigos inválidos.", "error");
        setIsLoading(false);
        return;
      }
      const ids = idArea.split(/\r?\n/).map((x) => x.trim());

      await CodeService.uploadCodes({
        codes: ids,
        promotionId,
      });
      onClose();
      setIsLoading(false);
      router.refresh();
    } catch (error) {
      if (error instanceof AppError) {
        fire(error.code, error.message, "error");
        setIsLoading(false);
        onClose();
      }
    }
  };

  return (
    <Modal modalProps={{ isOpen, onClose }}>
      <Stack spacing={5} p={{ base: 8 }}>
        <Text textAlign={"center"}>Inserta los códigos a subir. Un código por linea, sin comas.</Text>
        <Textarea onChange={(e) => setIdArea(e.target.value)} minH={400} />
        <Flex justify={"space-between"} mt={21} flexWrap={{ base: "wrap", md: "nowrap" }} gap={5}>
          <Button
            isLoading={isLoading}
            _hover={{ bgColor: "red.400", color: "white" }}
            onClick={() => onUploadCodes()}
            w={{ base: "100%" }}
          >
            Subir
          </Button>
          <Button
            isLoading={isLoading}
            variant={"outline"}
            borderColor={"#595959"}
            _hover={{ bgColor: "transparent" }}
            onClick={() => onClose()}
            w={{ base: "100%" }}
          >
            Cancelar
          </Button>
        </Flex>
      </Stack>
    </Modal>
  );
}
