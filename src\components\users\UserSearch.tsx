"use client";

import { useUsersPage } from "@app/hooks/useUsersPage";
import { CardIconTransparent } from "@app/public/svgs/CardIconTransparent";
import { Flex } from "@chakra-ui/react";
import React from "react";
import { Button } from "../common/Button";
import { Search } from "../common/Search";

export function UserSearch() {
  const { context } = useUsersPage();
  const { fetchUsers, offset } = context;
  const inputRef = React.useRef<HTMLInputElement | null>(null);

  const onSearch = (value: string) => {
    fetchUsers({
      detailed: true,
      compound: value,
      offset: offset.current,
    });
  };

  const onClick = () => {
    if (inputRef.current && inputRef.current.value.length === 0) {
      return;
    }
    onSearch(inputRef.current?.value || "");
  };

  const onEmpty = () => onSearch("");

  return (
    <Flex gap={13} alignItems={"center"} flexDir={{ base: "column", md: "row" }} w={"100%"}>
      <Search
        ref={inputRef}
        onSearch={onSearch}
        onEmpty={onEmpty}
        flexProps={{ flexBasis: "100%", width: { base: "100%", md: "auto" } }}
        placeholder="Ingresa nro de documento de identidad, correo, nombre o apellido..."
      />
      <Button
        w={{ base: "100%", md: "auto" }}
        color={"white"}
        fontSize={13}
        bg={"black"}
        _hover={{ bg: "black" }}
        onClick={onClick}
        leftIcon={<CardIconTransparent />}
      >
        Buscar
      </Button>
    </Flex>
  );
}
