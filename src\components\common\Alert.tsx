import {
  Alert as ChakraAlert,
  AlertIcon as ChakraAlertIcon,
  AlertTitle as ChakraAlertTitle,
  AlertDescription as ChakraAlertDescription,
  AlertProps as ChakraAlertProps,
} from "@chakra-ui/react";
import { PropsWithChildren } from "react";

type AlertProps = {
  title?: string;
} & ChakraAlertProps &
  PropsWithChildren;

export function Alert({ title, ...rest }: AlertProps) {
  return (
    <ChakraAlert
      borderRadius={8}
      color={"black"}
      mb={5}
      variant={{ base: "top-accent", md: "left-accent" }}
      flexDir={{ base: "column", md: "row" }}
      {...rest}
    >
      <ChakraAlertIcon />
      <ChakraAlertTitle>{title}</ChakraAlertTitle>
      <ChakraAlertDescription textAlign={{ base: "center", md: "left" }}>{rest.children}</ChakraAlertDescription>
    </ChakraAlert>
  );
}
