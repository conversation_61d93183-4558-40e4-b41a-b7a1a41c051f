import { BaseService, FetchResponse } from "@app/lib/service/baseService";
import { attribute } from "@mainframe-peru/types";

export class AttributeService extends BaseService {
  static async list(): Promise<FetchResponse<attribute.ListAttributesResponse>> {
    const response = await this.fetchData<attribute.ListAttributesResponse>(`${this.BASE_URL}/attribute/`, {
      method: "GET",
    });
    return response;
  }
}
