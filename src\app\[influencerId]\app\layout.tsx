import { AppAside } from "@app/components/app/AppAside";
import { AppHeader } from "@app/components/app/AppHeader";
import ErrorBoundary from "@app/components/common/Error";
import { InfluencerService } from "@app/services/influencerService";
import { Box, BoxProps } from "@chakra-ui/react";
import { SessionProvider } from "@contexts/SessionProvider";
import React from "react";

const contentPadding = {
  padding: "80px 15px 15px 15px",
};

const innerContentPadding = {
  padding: {
    base: "110px 30px 30px 30px",
    lg: "110px 60px 45px 60px",
  },
  overflow: "auto",
  width: "100%",
};

const asideProps: BoxProps = {
  minW: 233,
  height: "100%",
  overflowY: "auto",
  bg: "gray.400",
  display: {
    base: "none",
    xl: "flex",
  },
  gap: "15px",
  flexDir: "column",
  alignItems: "center",
  justifyContent: "space-between",
};

export async function generateStaticParams() {
  const influencers = await InfluencerService.getInfluencers();
  return influencers.map((inf) => ({
    influencerId: inf.id,
  }));
}

export default function AppLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { influencerId: string };
}) {
  return (
    <SessionProvider influencerId={params.influencerId}>
      <AppHeader />
      <Box display={"flex"} height={"100%"}>
        <AppAside {...asideProps} {...contentPadding} />
        <Box {...innerContentPadding} bg="#1F1F1F">
          <ErrorBoundary>{children}</ErrorBoundary>
        </Box>
      </Box>
    </SessionProvider>
  );
}
