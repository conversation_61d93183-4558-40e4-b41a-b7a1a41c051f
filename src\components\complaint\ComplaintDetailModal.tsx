"use client";

import { ComplaintsPageFilters, useComplaintsPage } from "@app/contexts/ComplaintsPageProvider";
import { Divider, Flex, Heading, Stack, Text, useToast } from "@chakra-ui/react";
import { Button } from "../common/Button";
import { Modal } from "../common/Modal";
import { ComplaintStatusTag } from "./ComplaintStatusTag";
import { IssueTypeTag } from "./IssueTypeTag";
import { ComplaintService } from "@app/services/complaintService";
import { AppError } from "@mainframe-peru/common-core";
import { useState } from "react";
import { common, complaint } from "@mainframe-peru/types";
import { SupportStatus } from "@mainframe-peru/types/build/common";
import { useSession } from "@app/hooks/useSession";

type ComplaintDetailModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export function ComplaintDetailModal({ isOpen, onClose }: ComplaintDetailModalProps) {
  const { currentComplaint } = useComplaintsPage();

  const {
    subject,
    description,
    email,
    firstName,
    lastName,
    documentType,
    phone,
    status,
    ageCategory,
    type,
    documentValue,
  } = currentComplaint;
  const fullName = `${firstName} ${lastName}`;

  return (
    <Modal modalProps={{ isOpen, onClose }} bodyProps={{ p: 8, minW: 600 }}>
      <Flex gap={3} align={"center"}>
        <IssueTypeTag type={type} />
        <ComplaintStatusTag status={status || "OPENED"} />
      </Flex>
      <Stack gap={8} mt={8}>
        <Stack gap={5}>
          <Heading size={"md"}>Datos de la queja o reclamo</Heading>
          <Divider />
          <Stack gap={5}>
            <Stack>
              <Text as={"small"}>Asunto:</Text>
              <Heading fontSize={18} noOfLines={3}>
                {subject}
              </Heading>
            </Stack>
            <Stack>
              <Text as={"small"}>Detalle:</Text>
              <Text fontSize={13}>{description}</Text>
            </Stack>
          </Stack>
        </Stack>
        <Stack gap={5}>
          <Heading size={"md"}>Datos del cliente</Heading>
          <Divider />
          <Stack gap={3} fontSize={13}>
            <Flex align={"center"} justify={"space-between"}>
              <Text>
                <Text as={"strong"}>Nombre:</Text> {fullName}
              </Text>
              <Text>
                <Text as={"strong"}>Email:</Text> {email}
              </Text>
            </Flex>
            <Flex align={"center"} justify={"space-between"}>
              <Text>
                <Text as={"strong"}>Teléfono: </Text>
                {phone}
              </Text>
              <Text>
                <Text as={"strong"}>Mayor de edad: </Text>
                {ageCategory === "ADULT" ? "Sí" : "No"}{" "}
              </Text>
            </Flex>
            <Flex align={"center"} justify={"space-between"}>
              <Text>
                <Text as={"strong"}>Tipo de documento:</Text> {documentType}
              </Text>
              <Text>
                <Text as={"strong"}>Nro documento:</Text> {documentValue}
              </Text>
            </Flex>
          </Stack>
        </Stack>
        <ModalButtons status={status || "CLOSED"} />
      </Stack>
    </Modal>
  );
}

function ModalButtons({ status }: { status: SupportStatus }) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { complaints, currentComplaint, filters, onSetComplaints, onSetCurrentComplaint } = useComplaintsPage();
  const session = useSession();

  const toast = useToast();

  if (status === "CLOSED") {
    return;
  }

  const onResolveIssue = async (status: common.SupportStatus) => {
    setIsLoading(true);
    try {
      const response = await ComplaintService.updateIssue({ id: currentComplaint.id, status });

      let complaintsUpdated = complaints.map((complaint) => {
        if (response.complaint.id === complaint.id) {
          return { ...complaint, status: response.complaint.status };
        }
        return { ...complaint };
      });

      for (const key in filters) {
        const filter = filters[key as keyof ComplaintsPageFilters];
        if (filter) {
          complaintsUpdated = complaintsUpdated.filter(
            (complaint) => complaint[key as keyof complaint.Complaint] === filter,
          );
        }
      }

      toast({
        status: "success",
        title: "Actualización Exitosa",
        description: `Se ha actualizado el reclamo ${response.complaint.subject}.`,
      });
      onSetComplaints(complaintsUpdated);
      onSetCurrentComplaint(response.complaint);
    } catch (error) {
      if (error instanceof AppError) {
        toast({ status: "error", title: error.code, description: error.message });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const isArchived = status === "ARCHIVED";
  const isOpened = status === "OPENED";
  const toggleArchiveStates = isArchived ? "OPENED" : "ARCHIVED";

  return (
    <>
      {session.isInPolicy("complaint", "PUT_ISSUE") && (
        <Flex gap={5} justify={"flex-end"} mt={8}>
          <Button isLoading={isLoading} onClick={() => onResolveIssue("CLOSED")} isDisabled={isOpened && isArchived}>
            Resolver
          </Button>
          <Button variant={"outline"} isLoading={isLoading} onClick={() => onResolveIssue(toggleArchiveStates)}>
            {isArchived ? "Desarchivar" : "Archivar"}
          </Button>
        </Flex>
      )}
    </>
  );
}
