import { Box, BoxProps } from "@chakra-ui/react";

export function VisaIcon({ width = "75px", height = "25px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 75 25" fill="none">
        <path
          d="M39.1161 8.17268C39.0737 11.5158 42.0954 13.3815 44.3718 14.4907C46.7106 15.6289 47.4962 16.3587 47.4873 17.3763C47.4694 18.9341 45.6216 19.6214 43.892 19.6482C40.8747 19.6951 39.1205 18.8336 37.7257 18.182L36.6388 23.2681C38.0381 23.9131 40.6292 24.4755 43.3162 24.5C49.623 24.5 53.7495 21.3867 53.7718 16.5595C53.7964 10.4334 45.298 10.0942 45.356 7.35587C45.3761 6.52567 46.1683 5.63967 47.9046 5.41426C48.7638 5.30045 51.1362 5.21341 53.8254 6.45202L54.881 1.53106C53.4348 1.00437 51.5758 0.5 49.2615 0.5C43.3251 0.5 39.1495 3.65566 39.1161 8.17268ZM65.0242 0.924028C63.8726 0.924028 62.9018 1.59578 62.4689 2.62684L53.4594 24.1385H59.7618L61.016 20.6726H68.7177L69.4452 24.1385H75L70.1527 0.924028H65.0242ZM65.9057 7.19518L67.7246 15.9123H62.7434L65.9057 7.19518ZM31.4746 0.924028L26.5068 24.1385H32.5124L37.478 0.924028H31.4746ZM22.5901 0.924028L16.3391 16.7247L13.8105 3.28966C13.5137 1.78994 12.342 0.924028 11.0409 0.924028H0.821847L0.679016 1.59801C2.77684 2.05328 5.16033 2.78752 6.60425 3.57309C7.48802 4.05291 7.7402 4.47248 8.03033 5.61289L12.8196 24.1385H19.1666L28.897 0.924028H22.5901Z"
          fill="#F8F8F8"
        />
      </svg>
    </Box>
  );
}
