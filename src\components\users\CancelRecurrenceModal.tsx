import { useSession } from "@app/hooks/useSession";
import { RecurrenceService } from "@app/services/recurrenceService";
import { Flex, Heading, Stack, Text, useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { useRouter, useSearchParams } from "next/navigation";
import React from "react";
import { Button } from "../common/Button";
import { Modal } from "../common/Modal";

export type CancelModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onOpen?: () => void;
};

export function CancelRecurrenceModal({ isOpen, onClose }: CancelModalProps) {
  const [isLoading, setIsLoading] = React.useState(false);

  const session = useSession();
  const id = useSearchParams().get("id");
  const router = useRouter();
  const toast = useToast();

  const onCancelRecurrence = async () => {
    setIsLoading(true);
    try {
      await RecurrenceService.cancelUserRecurrence(id!);
      onClose();
      router.refresh();
    } catch (error) {
      if (error instanceof AppError) {
        toast({
          title: error.code,
          description: ` ${error.message} id: ${session.admin?.id}`,
          status: "error",
          position: "top-right",
          duration: 5000,
          isClosable: true,
        });
        setIsLoading(false);
        onClose();
      }
    }
  };

  return (
    <Modal modalProps={{ isOpen, onClose }}>
      <Stack spacing={5} p={{ base: 8 }}>
        <Heading textAlign={"center"} size={"md"}>
          ¿Estás seguro de querer cancelar esta suscripción?
        </Heading>
        <Text textAlign={"center"}>Esta acción es irreversible.</Text>
        <Flex justify={"space-between"} mt={21} flexWrap={{ base: "wrap", md: "nowrap" }} gap={5}>
          <Button
            isLoading={isLoading}
            _hover={{ bgColor: "red.400", color: "white" }}
            onClick={() => onCancelRecurrence()}
            w={{ base: "100%" }}
          >
            Sí, deseo cancelar
          </Button>
          <Button
            isLoading={isLoading}
            variant={"outline"}
            borderColor={"#595959"}
            _hover={{ bgColor: "transparent" }}
            onClick={() => onClose()}
            w={{ base: "100%" }}
          >
            Cancelar
          </Button>
        </Flex>
      </Stack>
    </Modal>
  );
}
