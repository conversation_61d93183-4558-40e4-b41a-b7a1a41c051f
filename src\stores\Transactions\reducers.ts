import { ActionsDicionary } from "../types";
import { changeDate, changeLimit, changeStatus, loadTransactions, setCurrentTransaction } from "./actions";
import { TRANSACTION_ACTIONS } from "./constants";
import {
  SetCurrentTransactionAction,
  TransactionsActions,
  TransactionsChangeDateAction,
  TransactionsChangeLimitsRowsAction,
  TransactionsChangeStatusAction,
  TransactionsLoadAction,
  TransactionsPageStore,
} from "./types";

export function transactionsReducer(state: TransactionsPageStore, action: TransactionsActions) {
  const transactionsActionsDic: ActionsDicionary<TransactionsPageStore> = {
    [TRANSACTION_ACTIONS.LOAD_TRANSACTIONS]: loadTransactions(action as TransactionsLoadAction, state),
    [TRANSACTION_ACTIONS.FILTER_BY_STATUS]: changeStatus(action as TransactionsChangeStatusAction, state),
    [TRANSACTION_ACTIONS.LIMIT_ROWS]: changeLimit(action as TransactionsChangeLimitsRowsAction, state),
    [TRANSACTION_ACTIONS.CHANGE_DATE]: changeDate(action as TransactionsChangeDateAction, state),
    [TRANSACTION_ACTIONS.SET_CURRENT_TRANSACTION]: setCurrentTransaction(action as SetCurrentTransactionAction, state),
  };
  return transactionsActionsDic[action.type] || state;
}
