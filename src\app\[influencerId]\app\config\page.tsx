import { configuracion } from "@app/components/common/Breadcrumb";
import { ConfigForm } from "@app/components/config/ConfigForm";
import { ContainerLayout } from "@app/layouts/ContainerLayout";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Configuración",
};

export default function ConfigPage() {
  return (
    <ContainerLayout>
      <PageLayout breadcrumb={configuracion} title="Configuración">
        <ConfigForm />
      </PageLayout>
    </ContainerLayout>
  );
}
