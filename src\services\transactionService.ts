import { BaseService } from "@app/lib/service/baseService";
import { Transaction } from "@app/lib/transactions/types";
import { transaction } from "@mainframe-peru/types";

export class TransactionService extends BaseService {
  /**
   * Obtiene pagos
   */
  static async getTransactions(filter: transaction.ListTransactionsRequest): Promise<Transaction[]> {
    const url = `${this.BASE_URL}/transaction/list-transactions?${this.buildQueryParams(filter)}`;
    const { data } = await this.fetchData<{ items: Transaction[] }>(url, {
      headers: {
        Accept: "application/json",
      },
    });
    return data.items;
  }

  static async getTransactionsCsv(filter: transaction.ListTransactionsRequest): Promise<void> {
    const url = `${this.BASE_URL}/transaction/list-transactions?${this.buildQueryParams(filter)}`;
    const { data, headers } = await this.fetchData<string>(url, {
      headers: {
        Accept: "text/csv",
      },
    });
    this.downloadCsv(data, headers!.get("Content-Disposition"));
  }

  /**
   * Obtiene los pagos de un usuario específico en SSR
   */
  static async getUserTransactionsSsr(userId: string | number, token?: string): Promise<Transaction[]> {
    try {
      const url = `${this.BASE_URL}/transaction/list-transactions?${this.buildQueryParams({ userId })}`;
      const headers = { Cookie: `session=${token}`, Accept: "application/json" };
      const { data } = await this.fetchData<{ items: Transaction[] }>(url, { headers });
      return data.items;
    } catch {
      return [];
    }
  }

  static async getUserTransactions(userId: string | number, detailed?: boolean): Promise<Transaction[]> {
    try {
      const url = `${this.BASE_URL}/transaction/list-transactions?${this.buildQueryParams({ userId, detailed })}`;
      const { data } = await this.fetchData<{ items: Transaction[] }>(url, {
        headers: { Accept: "application/json" },
        credentials: "include",
      });
      return data.items;
    } catch {
      return [];
    }
  }
}
