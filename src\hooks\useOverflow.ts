import React from "react";

export function useOverflow<T extends HTMLElement>(ref: React.MutableRefObject<T | null>, flag: boolean) {
  const [overflow, setOverflow] = React.useState<boolean>(false);
  React.useLayoutEffect(() => {
    const current = ref.current;
    if (!current) return;
    const elementClientRect = current.getBoundingClientRect();

    // check if the datepicker overflow the inner width of the browser
    if (elementClientRect.right < window.innerWidth) return;
    setOverflow(true);

    // cleanup
    return () => {
      if (!current) return;
      setOverflow(false);
    };
  }, [flag, ref]);

  return overflow;
}
