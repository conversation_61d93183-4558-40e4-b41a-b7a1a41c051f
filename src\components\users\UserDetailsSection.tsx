"use client";
import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { Box, Flex, Heading, Skeleton, Stack, Text, useDisclosure } from "@chakra-ui/react";
import { format } from "date-fns";
import React from "react";
import { Alert } from "../common/Alert";
import { Button } from "../common/Button";
import { CancelRecurrence } from "./CancelRecurrence";
import { NoRecurrenceFound } from "./NoRecurrenceFound";
import { UserCard } from "./UserCard";
import { PersonalDetailForm } from "./UserDetailForm";
import { useUserDetailsPage } from "./UserDetailProvider";
import { UserRecurrenceDetail } from "./UserRecurrenceDetail";
import { ActivityLogModal } from "./ActivityLogModal";

const RenderAlert = ({ dateEnd, fullname }: { dateEnd: string; fullname: string }) => {
  const { isLoading } = useUserDetailsPage();

  if (isLoading) {
    return <Skeleton isLoaded={!isLoading} h={55} mb={5} borderRadius={21} />;
  }

  if (!dateEnd) return null;
  return (
    <Alert status="error" title="Alerta de suscripción:">
      <Text>
        La suscripción de <Text as="strong">{fullname}</Text> terminará el <Text as="strong">{dateEnd}</Text>.
      </Text>
    </Alert>
  );
};

const RenderUserCard = ({ fullname, dateEnd }: { fullname: string; dateEnd: string }) => {
  const { recurrence, cards } = useUserDetailsPage();
  if (!recurrence) {
    return <NoRecurrenceFound />;
  }

  const defaultCard = cards.find((card) => card.default);
  const cardBrand = recurrence.type === "MANUAL" || !defaultCard ? "Yape" : defaultCard.brand;
  const cardNumber = recurrence.type === "CARD" && defaultCard ? defaultCard.number : "";

  return (
    <React.Fragment>
      <UserCard brand={cardBrand} name={fullname} number={cardNumber} amount={recurrence.plan.amount} />
      {!dateEnd && <CancelRecurrence />}
    </React.Fragment>
  );
};

export function UserDetailSection() {
  const { recurrence, user, isLoading, activityLogs, isLoadingActivityLogs, fetchActivityLogs } = useUserDetailsPage();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const fullname = `${user?.firstName} ${user?.lastName}`;
  const dateEnd = recurrence?.endDate ? format(new Date(recurrence.endDate), TABLE_STANDARD_FORMAT_DATE) : "";

  const handleActivityClick = async () => {
    await fetchActivityLogs();
    onOpen();
  };

  return (
    <React.Fragment>
      <RenderAlert dateEnd={dateEnd} fullname={fullname} />
      <Flex mb={5} flexDir={{ base: "column", md: "row" }} justify="space-between" gap={5}>
        <Box width={{ base: "100%", md: "50%", lg: "50%", xl: "60%" }}>
          <Stack spacing={5}>
            <PersonalDetailForm />
            <UserRecurrenceDetail
              amount={recurrence?.plan?.amount}
              frecuency={recurrence?.plan?.frequency}
              renewalDate={recurrence?.renewalDate}
            />
          </Stack>
        </Box>
        <Box width={{ base: "100%", md: "50%", lg: "50%", xl: "40%" }}>
          <Skeleton isLoaded={!isLoading} borderRadius={21} height="100%">
            <Box bg="black" color="white" borderRadius={21} p={6} height={{ base: "fit-content", xl: "100%" }}>
              <Flex justify="space-between" align="center" mb={8}>
                <Heading size="md" color="#5C73F2">
                  Método de pago
                </Heading>
                <Button
                  size="sm"
                  variant="outline"
                  borderColor="#5C73F2"
                  color="#5C73F2"
                  _hover={{ bg: "#5C73F2", color: "white" }}
                  onClick={handleActivityClick}
                >
                  Actividad
                </Button>
              </Flex>
              <RenderUserCard dateEnd={dateEnd} fullname={fullname} />
            </Box>
          </Skeleton>
        </Box>
      </Flex>

      <ActivityLogModal
        isOpen={isOpen}
        onClose={onClose}
        activityLogs={activityLogs}
        isLoading={isLoadingActivityLogs}
      />
    </React.Fragment>
  );
}
