import { BaseService } from "@app/lib/service/baseService";
import { ListActivityLogResponse } from "@app/lib/activity-log/types";
import { common } from "@mainframe-peru/types";

export class ActivityLogService extends BaseService {
  /**
   * Obtiene el activity log filtrado
   */
  static async getActivityLog(filter: common.ListActivityLogRequest): Promise<ListActivityLogResponse> {
    const url = `${this.BASE_URL}/activity-log?${this.buildQueryParams(filter)}`;
    const { data } = await this.fetchData<ListActivityLogResponse>(url, {
      headers: {
        Accept: "application/json",
      },
      credentials: "include",
    });
    return data;
  }
}
