"use client";

import { HamburgerIcon } from "@chakra-ui/icons";
import { Text, TextProps, useDisclosure } from "@chakra-ui/react";
import { Drawer } from "@components/common/Drawer";
import React, { PropsWithChildren } from "react";
import { Button } from "../common/Button";
import { useSession } from "@app/hooks/useSession";

const titleProps: TextProps = {
  fontWeight: "bold",
  textTransform: "uppercase",
  fontSize: "16px",
};

export function AppDrawer({ children }: PropsWithChildren) {
  const { isOpen, onClose, onOpen } = useDisclosure();
  const session = useSession();

  return (
    <React.Fragment>
      <Button
        color={"white"}
        p={0}
        bg={"transparent"}
        _hover={{ bg: "transparent" }}
        display={{ base: "flex", xl: "none" }}
        gap={"12px"}
        onClick={onOpen}
      >
        <HamburgerIcon />
        <Text {...titleProps}>{session.admin?.influencerId}</Text>
      </Button>
      <Drawer drawerProps={{ placement: "left", isOpen, onClose }}>{children}</Drawer>
    </React.Fragment>
  );
}
