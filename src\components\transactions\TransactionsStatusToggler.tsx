"use client";

import { CrossIcon } from "@app/public/svgs/CrossIcon";
import { PlusIcon } from "@app/public/svgs/PlusIcon";
import { BoxProps, ButtonProps, Flex, FlexProps, Text } from "@chakra-ui/react";
import { Button } from "../common/Button";
import { Enums, TransactionState } from "@mainframe-peru/types/build/common";
import { useTransactionsPage } from "@app/hooks/useTransactionsPage";
import { TRANSACTION_ACTIONS } from "@app/stores/Transactions/constants";

const linearGradientGreenToBlue = "linear-gradient(90deg, #00FF75 0%, #24ADFA 100%)";
const buttonProps: ButtonProps = {
  height: "fit-content",
  py: "5px",
  fontSize: "12px",
  px: "8px",
  color: "white.400",
  borderRadius: "25px",
  bg: "gray.300",
  _hover: {
    color: "black",
    bg: linearGradientGreenToBlue,
    cursor: "pointer",
  },
};

const buttonIconProps: BoxProps = {
  width: "13px",
  height: "13px",
  bg: "white.400",
  p: "2px",
  borderRadius: "100%",
};

export function PaymentsStatusToggler({ ...props }: FlexProps) {
  const { store, dispatch } = useTransactionsPage();

  const onToggleStatus = (status: TransactionState) => {
    dispatch({ type: TRANSACTION_ACTIONS.FILTER_BY_STATUS, payload: { status } });
  };

  return (
    <Flex gap={"15px"} justifyContent={"space-between"} {...props}>
      <Text fontSize={"15px"}>Total Pagos</Text>
      <Flex gap={"15px"} alignItems={"center"}>
        <Button
          {...buttonProps}
          bgImage={
            store.filters.status === Enums.TransactionState.Enum.SUCCESS ? linearGradientGreenToBlue : "gray.300"
          }
          color={store.filters.status === Enums.TransactionState.Enum.SUCCESS ? "black" : "white.400"}
          rightIcon={<PlusIcon {...buttonIconProps} />}
          onClick={() => onToggleStatus(Enums.TransactionState.Enum.SUCCESS)}
        >
          Exitosas
        </Button>
        <Button
          {...buttonProps}
          bgImage={store.filters.status === Enums.TransactionState.Enum.FAIL ? linearGradientGreenToBlue : "gray.300"}
          color={store.filters.status === Enums.TransactionState.Enum.FAIL ? "black" : "white.400"}
          rightIcon={<CrossIcon {...buttonIconProps} />}
          onClick={() => onToggleStatus(Enums.TransactionState.Enum.FAIL)}
        >
          Fallidas
        </Button>
      </Flex>
    </Flex>
  );
}
