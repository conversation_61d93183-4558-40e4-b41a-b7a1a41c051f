"use client";

import { useTransactionsChart } from "@app/hooks/Reports/useTransactionsChart";
import { ReportsService } from "@app/services/reportService";
import { Box, Flex, Skeleton, Text } from "@chakra-ui/react";
import React from "react";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from "recharts";

type BigNumberProps = { title: string; description: string; number?: number };

const BigNumber = (props: BigNumberProps) => (
  <Box
    flex={1}
    bgColor={"black"}
    borderRadius={16}
    p="24px"
    display="flex"
    flexDirection="column"
    justifyContent="center"
  >
    <Box textColor="blue.500" fontSize="20px" fontWeight="bold">
      {props.title}
    </Box>
    <Text textColor={"gray.100"} fontSize="14px">
      {props.description}
    </Text>
    <Box fontSize="40px">{props.number || "-"}</Box>
  </Box>
);

export default function TransactionsChart() {
  const { transactionsChartStore } = useTransactionsChart();
  const maxSuccessCount = Math.max(...transactionsChartStore.transactions.map((d) => d.successCount));
  const maxErrorCount = Math.max(...transactionsChartStore.transactions.map((d) => d.errorCount));
  const maxY = Math.ceil(Math.max(maxSuccessCount, maxErrorCount) * 1.1);

  const [totalSubs, setTotalSubs] = React.useState<number>();
  const [totalUsers, setTotalUsers] = React.useState<number>();
  const [planTotals, setPlanTotals] = React.useState<BigNumberProps[]>();

  React.useEffect(() => {
    loadTotalSubs();
    loadTotalUsers();
  }, []);

  const loadTotalSubs = async () => {
    try {
      const result = await ReportsService.getActiveRecurrenceCount();
      setTotalSubs(result.total);
      setPlanTotals(
        result.plan.map((p) => ({
          title: p.name,
          description: `Personas suscritas al plan ${p.id}.`,
          number: p.count,
        })),
      );
    } catch {}
  };

  const loadTotalUsers = async () => {
    try {
      const result = await ReportsService.getUserCount();
      setTotalUsers(result.total);
    } catch {}
  };

  const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      return (
        <Box backgroundColor={"gray.400"} p={3} borderRadius={16}>
          <Text fontWeight={"bold"}>{`Fecha: ${label}`}</Text>
          {payload.map((entry, index) => (
            <Text key={`item-${index}`} style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value}`}
            </Text>
          ))}
        </Box>
      );
    }
    return null;
  };

  return (
    <Flex width={"100%"} height={{ base: "100%", md: "90%" }} minHeight={400} gap="16px" direction="column">
      <Flex gap="16px" direction={{ base: "column", md: "row" }}>
        <Skeleton gap="16px" display="flex" flexDirection="row" isLoaded={!!(totalSubs && totalUsers)} flex={1}>
          <BigNumber title="Suscripciones" description="Total de personas suscritas a la fecha." number={totalSubs} />
          <BigNumber
            title="Usuarios registrados"
            description="Total de usuarios registrados a la fecha."
            number={totalUsers}
          />
        </Skeleton>
        <Skeleton gap="16px" display="flex" flexDirection="row" isLoaded={!!planTotals} flex={1}>
          {planTotals?.map((p) => (
            <BigNumber key={p.title} title={p.title} description={p.description} number={p.number} />
          ))}
        </Skeleton>
      </Flex>
      <Flex
        direction="column"
        width={"100%"}
        height={{ base: "100%", md: "90%" }}
        minHeight={400}
        bgColor={"black"}
        borderRadius={16}
        pl={2}
        pt={8}
        pr={8}
        pb={8}
      >
        <Box px="36px">
          <Text textColor="blue.500" fontSize="20px" fontWeight="bold">
            Transacciones
          </Text>
          <Text textColor={"gray.100"} mb={5}>
            Resumen de las transacciones de los últimos 30 días, por resultado.
          </Text>
        </Box>
        <Box flex={1}>
          <ResponsiveContainer width="100%" height={"100%"}>
            {transactionsChartStore.transactions && (
              <LineChart data={transactionsChartStore.transactions}>
                <defs>
                  <linearGradient id="successGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#00FF75" />
                    <stop offset="100%" stopColor="#24ADFA" />
                  </linearGradient>

                  <linearGradient id="errorGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#E04C4A" />
                    <stop offset="100%" stopColor="#FFD02A" />
                  </linearGradient>
                </defs>

                <CartesianGrid vertical={false} stroke={"#737373"} strokeWidth={0.3} />
                <XAxis dataKey="date" tick={{ fill: "white" }} />
                <YAxis allowDecimals={false} domain={[0, maxY]} tick={{ fill: "white" }} />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  wrapperStyle={{
                    fontWeight: "bold",
                    marginBottom: "-5px",
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="successCount"
                  stroke="url(#successGradient)"
                  strokeWidth={4}
                  name="Exitosas"
                />
                <Line
                  type="monotone"
                  dataKey="errorCount"
                  stroke="url(#errorGradient)"
                  strokeDasharray="5 5"
                  name="Fallidas"
                />
              </LineChart>
            )}
          </ResponsiveContainer>
        </Box>
      </Flex>
    </Flex>
  );
}
