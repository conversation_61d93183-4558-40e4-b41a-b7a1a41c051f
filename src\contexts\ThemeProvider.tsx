"use client";

import React from "react";
import { ChakraProvider, extendTheme } from "@chakra-ui/react";
import { components, styles, colors } from "@lib/theme/theme.config";

function ThemeProvider({ children }: React.PropsWithChildren) {
  const theme = extendTheme({
    colors,
    config: {
      initialColorMode: "dark",
      cssVarPrefix: "mainframe",
      useSystemColorMode: false,
    },
    styles,
    components,
  });

  return (
    <ChakraProvider toastOptions={{ defaultOptions: { position: "top-right" } }} theme={theme}>
      {children}
    </ChakraProvider>
  );
}

export { ThemeProvider };
