import { lottery } from "@app/components/common/Breadcrumb";
import { EventForm } from "@app/components/event/EventForm";
import { EventFormProvider } from "@app/components/event/EventForm/EventFormProvider";
import { ContainerLayout } from "@app/layouts/ContainerLayout";
import { PageLayout } from "@app/layouts/PageLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Mainframe App | Crear evento",
};

export default function CreateEventPage() {
  return (
    <ContainerLayout>
      <PageLayout breadcrumb={{ parent: lottery, name: "Crear", url: "/premios-y-eventos/crear" }} title="Crear evento">
        <EventFormProvider>
          <EventForm />
        </EventFormProvider>
      </PageLayout>
    </ContainerLayout>
  );
}
