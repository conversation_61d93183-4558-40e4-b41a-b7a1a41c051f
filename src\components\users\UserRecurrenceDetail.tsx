"use client";

import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { Flex, FormControl, FormControlProps, FormLabel, Heading, Input, Skeleton, Stack } from "@chakra-ui/react";
import { format } from "date-fns";
import { useUserDetailsPage } from "./UserDetailProvider";

const formControlProps: FormControlProps = {
  maxW: {
    base: "100%",
    xl: "calc(33.33% - 1rem)",
  },
};

type UserRecurrenceDetail = Partial<{
  renewalDate: Date;
  amount: string;
  frecuency: number;
}>;

export function UserRecurrenceDetail({ renewalDate, amount, frecuency }: UserRecurrenceDetail) {
  const { isLoading } = useUserDetailsPage();

  let currentRenewalDate = "-";
  if (renewalDate) {
    const renewalDateFormated = format(new Date(renewalDate), TABLE_STANDARD_FORMAT_DATE);
    currentRenewalDate = renewalDateFormated;
  }

  const frecuencyMap: { [k: string | number]: string } = {
    30: "Mensual",
    360: "Anual",
    default: "-",
  };

  return (
    <Skeleton isLoaded={!isLoading} borderRadius={21}>
      <Stack bg="black" color="white" borderRadius={21} p={5} spacing={5}>
        <Heading size={"md"} color={"#5C73F2"}>
          Detalle de la suscripción
        </Heading>
        <Flex gap={5} flexWrap={"wrap"} justify={"space-between"}>
          <FormControl {...formControlProps}>
            <FormLabel noOfLines={1} fontSize={13}>
              Prox. Fecha de pago
            </FormLabel>
            <Input value={currentRenewalDate} readOnly bgColor={"#262626"} />
          </FormControl>
          <FormControl {...formControlProps}>
            <FormLabel fontSize={13}>Tipo de pago</FormLabel>
            <Input value={frecuencyMap[frecuency || "default"]} readOnly bgColor={"#262626"} />
          </FormControl>
          <FormControl {...formControlProps}>
            <FormLabel fontSize={13}>Monto</FormLabel>
            <Input value={`S/ ${Number(amount || "0").toFixed(2)}`} readOnly bgColor={"#262626"} />
          </FormControl>
        </Flex>
      </Stack>
    </Skeleton>
  );
}
