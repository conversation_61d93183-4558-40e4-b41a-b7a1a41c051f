import { BaseService } from "@app/lib/service/baseService";
import { report as re } from "@mainframe-peru/types";

export class ReportsService extends BaseService {
  static async getTransactionsCountByStateReport(influencerId: string): Promise<re.TransactionCountByStateResponse> {
    const { data } = await this.fetchData<re.TransactionCountByStateResponse>(
      `${this.BASE_URL}/report/transactions-count-by-state?influencerId=${influencerId}`,
    );
    return data;
  }

  static async getActiveRecurrenceCount(): Promise<re.GetActiveRecurrenceResponseCount> {
    const { data } = await this.fetchData<re.GetActiveRecurrenceResponseCount>(
      `${this.BASE_URL}/report/recurrence-by-plan`,
    );
    return data;
  }

  static async getUserCount(): Promise<re.GetUserResponseCount> {
    const { data } = await this.fetchData<re.GetUserResponseCount>(`${this.BASE_URL}/report/user-count`);
    return data;
  }

  static startDownloadCsvText(text: string) {
    const blob = new Blob([text], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    const date = new Date();
    const dayDate = date.getDate();
    const month = date.getMonth();
    const year = date.getFullYear();
    const filename = `usuarios-${dayDate}${month}${year}.csv`;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  static async downloadUsersList() {
    const { data } = await this.fetchData<string>(`${this.BASE_URL}/report/list-subscribers`, {
      headers: { "Content-Type": "text/csv", credentials: "include" },
    });
    this.startDownloadCsvText(data);
  }
}
