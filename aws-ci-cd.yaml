AWSTemplateFormatVersion: 2010-09-09
Description: Creates CodeBuild and CodePipelines for influencer-admin-panel
Parameters:
  ServiceName:
    Type: String
    Default: influencer-admin-panel

  EnvName:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The environment name.
    Default: /env/name

Conditions:
  IsDev: !Equals [!Ref EnvName, dev]
  IsStg: !Equals [!Ref EnvName, stg]
  IsProd: !Equals [!Ref EnvName, prod]

Mappings:
  EnvMap:
    dev:
      Branch: dev
    stg:
      Branch: stg
    prod:
      Branch: main

Resources:
  Pipeline:
    Type: AWS::CodePipeline::Pipeline
    Properties:
      Name: !Sub ${ServiceName}
      RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn
      ArtifactStore:
        Type: S3
        Location: !ImportValue CI-CD-ArtifactBucket
        EncryptionKey:
          Id: !ImportValue CI-CD-ArtifactBucketKMSKeyId
          Type: KMS
      Stages:
        - Name: FetchSource
          Actions:
            - Name: GitHub
              ActionTypeId:
                Category: Source
                Owner: AWS
                Version: 1
                Provider: CodeStarSourceConnection
              Configuration:
                ConnectionArn: !ImportValue CI-CD-GithubConnection
                FullRepositoryId: !Sub Mainframe-Peru/${ServiceName}
                BranchName: !FindInMap [EnvMap, !Ref EnvName, Branch]
              OutputArtifacts:
                - Name: Source
              RunOrder: 1

        - Name: MakeInfrastructure
          Actions:
            - Name: CreateChangeSet
              InputArtifacts:
                - Name: Source
              ActionTypeId:
                Category: Deploy
                Owner: AWS
                Version: 1
                Provider: CloudFormation
              Configuration:
                StackName: !Sub ${ServiceName}
                ChangeSetName: !Sub ${ServiceName}-changeset
                ActionMode: CHANGE_SET_REPLACE
                Capabilities: CAPABILITY_NAMED_IAM
                TemplatePath: Source::aws-template.yaml
                RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn
              RunOrder: 1
              RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn

            - !If
              - IsProd
              - Name: ApproveProdDeployment
                RunOrder: 2
                ActionTypeId:
                  Category: Approval
                  Owner: AWS
                  Version: 1
                  Provider: Manual

              - !Ref AWS::NoValue

            - Name: ExecuteChangeSet
              ActionTypeId:
                Category: Deploy
                Owner: AWS
                Version: 1
                Provider: CloudFormation
              Configuration:
                StackName: !Sub ${ServiceName}
                ChangeSetName: !Sub ${ServiceName}-changeset
                ActionMode: CHANGE_SET_EXECUTE
                RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn
              InputArtifacts:
                - Name: Source
              RunOrder: 3
              RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn

        - Name: BuildAndDeploy
          Actions:
            - Name: CodeBuild
              ActionTypeId:
                Category: Build
                Owner: AWS
                Version: 1
                Provider: CodeBuild
              InputArtifacts:
                - Name: Source
              OutputArtifacts:
                - Name: BuildOutput
              Configuration:
                ProjectName: !Sub ${ServiceName}-build-deploy
              RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn
