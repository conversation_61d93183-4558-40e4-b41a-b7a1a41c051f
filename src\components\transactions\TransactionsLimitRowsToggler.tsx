"use client";

import { ButtonProps, Flex, FlexProps, Text } from "@chakra-ui/react";
import { Button } from "../common/Button";
import { useTransactionsPage } from "@app/hooks/useTransactionsPage";
import { TRANSACTION_ACTIONS } from "@app/stores/Transactions/constants";

export const buttonLimitProps: ButtonProps = {
  width: "fit-content",
  height: "fit-content",
  color: "white",
  padding: "8px 0",
  bgColor: "gray.300",
  fontSize: "13px",
  _hover: {
    color: "black",
    bgColor: "green.300",
  },
};

const limits = [10, 20, 50, 100];
export function PaymentsLimitRowsToggler({ ...props }: FlexProps) {
  const { store, dispatch } = useTransactionsPage();

  const onToggleLimit = (limit: number) => {
    dispatch({
      type: TRANSACTION_ACTIONS.LIMIT_ROWS,
      payload: {
        limit,
      },
    });
  };

  return (
    <Flex alignItems={"center"} gap={"15px"} justifyContent={{ base: "space-between", lg: "stretch" }} {...props}>
      <Text fontSize={"15px"}>Cargar Filas</Text>
      <Flex alignItems={"center"} gap={"13px"}>
        {limits.map((limit) => (
          <Button
            {...buttonLimitProps}
            key={limit}
            bgColor={store.filters.limit === limit ? "green.300" : "gray.300"}
            color={store.filters.limit === limit ? "black" : "white"}
            onClick={() => onToggleLimit(limit)}
          >
            {limit}
          </Button>
        ))}
      </Flex>
    </Flex>
  );
}
