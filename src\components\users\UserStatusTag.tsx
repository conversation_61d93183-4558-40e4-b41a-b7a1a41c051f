import { Text } from "@chakra-ui/react";
import { Tag } from "../common/Tag";

export function SubscriptionStatusTag({ status }: { status: "ACTIVE" | "NOT_ACTIVE" | string }) {
  const statusProps: Record<string, { color: string; text: string }> = {
    ACTIVE: {
      color: "green.400",
      text: "Activa",
    },
    NOT_ACTIVE: {
      color: "#E04C4A",
      text: "Inactiva",
    },
  };
  return (
    <Tag>
      Suscripción:{" "}
      <Text as={"span"} textTransform={"uppercase"} color={statusProps[status].color}>
        {statusProps[status].text}
      </Text>
    </Tag>
  );
}
