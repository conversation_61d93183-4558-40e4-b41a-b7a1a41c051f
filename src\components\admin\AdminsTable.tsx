"use client";

import { TABLE_STANDARD_FORMAT_DATE } from "@app/lib/common/constants";
import { EditIcon } from "@chakra-ui/icons";
import { Box } from "@chakra-ui/react";
import { admin } from "@mainframe-peru/types";
import { format } from "date-fns";
import Link from "next/link";
import { Table } from "../common/Table";
import { useAdminsPage } from "@app/contexts/AdminsPageProvider";
import { useSession } from "@app/hooks/useSession";

export function AdminsTable() {
  const { admins, isLoading } = useAdminsPage();

  const makeRows = (admins: admin.GetAdminResponse[]) => {
    return admins.map((admin) => {
      const { id, email, firstName, lastName, createdAt } = admin;
      const creationDateFormated = createdAt ? format(new Date(createdAt), TABLE_STANDARD_FORMAT_DATE) : "";

      return [
        id,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {email}
        </Box>,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {firstName}
        </Box>,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {lastName}
        </Box>,
        creationDateFormated,
        <EditRow key={id} id={id} />,
      ];
    });
  };

  return (
    <Table
      isLoading={isLoading}
      rows={makeRows(admins)}
      boxProps={{ mt: 34 }}
      headers={["Id", "Email", "Nombres", "Apellidos", "Fecha de creación", ""]}
    />
  );
}

function EditRow({ id }: { id: number }) {
  const session = useSession();
  return (
    <Link href={`/${session.admin?.influencerId}/app/admins/editar/?id=${id}`}>
      <EditIcon />
    </Link>
  );
}
