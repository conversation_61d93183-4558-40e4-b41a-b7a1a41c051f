"use client";

import { usePagination } from "@app/hooks/usePagination";
import { UserService } from "@app/services/userService";
import { USERS_PAGE_ACTION } from "@app/stores/Users/<USER>";
import { influencerUsersReducer } from "@app/stores/Users/<USER>";
import { USERS_PAGE_STORE } from "@app/stores/Users/<USER>";
import { UsersPageActions, UsersPageStore } from "@app/stores/Users/<USER>";
import { useToast } from "@chakra-ui/react";
import { AppError } from "@mainframe-peru/common-core";
import { ListUsersRequest } from "@mainframe-peru/types/build/user";
import React from "react";

export const UsersPageContext = React.createContext({} as UsersPageStore);
export const UsersPageDispatchContext = React.createContext({} as React.Dispatch<UsersPageActions>);

export type UsersContextType = {
  fetchUsers: (params?: ListUsersRequest) => Promise<void>;
  offset: React.MutableRefObject<number>;
  onIncrement: () => void;
  onDecrement: () => void;
};

export const UsersContext = React.createContext({} as UsersContextType);

export type UsersPageProviderProps = {
  children: React.ReactNode;
};

export function UsersPageProvider({ children }: UsersPageProviderProps) {
  const [store, dispatch] = React.useReducer(influencerUsersReducer, USERS_PAGE_STORE);
  const { offset, onIncrement, onDecrement } = usePagination();
  const toast = useToast();

  const fetchUsers = React.useCallback(
    async (params?: ListUsersRequest) => {
      dispatch({
        type: USERS_PAGE_ACTION.LOAD_USERS,
        payload: {
          isLoading: true,
        },
      });
      try {
        const users = await UserService.getUsers({
          detailed: true,
          ...params,
        });
        dispatch({
          type: USERS_PAGE_ACTION.LOAD_USERS,
          payload: {
            isLoading: false,
            users,
          },
        });
      } catch (error) {
        dispatch({
          type: USERS_PAGE_ACTION.LOAD_USERS,
          payload: {
            isLoading: false,
          },
        });
        if (error instanceof AppError) {
          toast({
            status: "error",
            title: error.name,
            description: error.message,
            isClosable: true,
          });
        }
      }
    },
    [toast],
  );

  React.useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return (
    <UsersPageContext.Provider value={store}>
      <UsersPageDispatchContext.Provider value={dispatch}>
        <UsersContext.Provider value={{ fetchUsers, offset, onIncrement, onDecrement }}>
          {children}
        </UsersContext.Provider>
      </UsersPageDispatchContext.Provider>
    </UsersPageContext.Provider>
  );
}
