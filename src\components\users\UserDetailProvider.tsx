"use client";

import { useToast } from "@app/hooks/useToast";
import { PageLayout } from "@app/layouts/PageLayout";
import { Card } from "@app/lib/card/type";
import { Transaction } from "@app/lib/transactions/types";
import { ActivityLogItem } from "@app/lib/activity-log/types";
import { CardService } from "@app/services/cardService";
import { RecurrenceService } from "@app/services/recurrenceService";
import { TransactionService } from "@app/services/transactionService";
import { UserService } from "@app/services/userService";
import { ActivityLogService } from "@app/services/activityLogService";
import { AppError } from "@mainframe-peru/common-core";
import { GetRecurrenceResponse } from "@mainframe-peru/types/build/recurrence";
import { GetUserResponse } from "@mainframe-peru/types/build/user";
import React, { PropsWithChildren } from "react";
import { suscriptores } from "../common/Breadcrumb";
import { useSearchParams } from "next/navigation";
import { Heading } from "@chakra-ui/react";

type UserDetailContextValues = {
  transactions: Transaction[];
  user: GetUserResponse | undefined;
  isLoading: boolean;
  cards: Card[];
  recurrence: GetRecurrenceResponse | undefined;
  activityLogs: ActivityLogItem[];
  isLoadingActivityLogs: boolean;
  fetchActivityLogs: () => Promise<void>;
};

const UserDetailContext = React.createContext({} as UserDetailContextValues);

export const useUserDetailsPage = () => {
  return React.useContext(UserDetailContext);
};

export function UserDetailProvider({ children }: PropsWithChildren) {
  const id = useSearchParams().get("id");
  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [user, setUser] = React.useState<GetUserResponse | undefined>();
  const [transactions, setUserTransactions] = React.useState<Transaction[]>([]);
  const [cards, setCards] = React.useState<Card[]>([]);
  const [recurrence, setRecurrence] = React.useState<GetRecurrenceResponse | undefined>();
  const [activityLogs, setActivityLogs] = React.useState<ActivityLogItem[]>([]);
  const [isLoadingActivityLogs, setIsLoadingActivityLogs] = React.useState(false);
  const { fire } = useToast();

  const fetchActivityLogs = React.useCallback(async () => {
    if (!user?.id) return;

    setIsLoadingActivityLogs(true);
    try {
      const response = await ActivityLogService.getActivityLog({ clientId: user.id });
      setActivityLogs(response.items);
    } catch (error) {
      if (error instanceof AppError) {
        fire(error.code, error.message, "error");
      }
    } finally {
      setIsLoadingActivityLogs(false);
    }
  }, [user?.id, fire]);

  React.useEffect(() => {
    if (id) {
      const fetchUserDetails = async () => {
        try {
          const response = await UserService.getUserById(id);
          setUser(response);
          if (response.id) {
            const transactions = await TransactionService.getUserTransactions(response.id, true);
            setUserTransactions(transactions);

            const userRecurrence = await RecurrenceService.getUserRecurrence(response.id);
            setRecurrence(userRecurrence);

            const userCards = await CardService.getUserCards(response.id);
            setCards(userCards);
          }
        } catch (error) {
          if (error instanceof AppError) {
            fire(error.code, error.message, "error");
          }
        } finally {
          setIsLoading(false);
        }
      };
      fetchUserDetails();
    }
  }, [id, fire]);

  const fullname = `${user?.firstName} ${user?.lastName}`;
  const subscriptorBreadcrumb = {
    parent: suscriptores,
    name: fullname,
    url: `/usuarios/${user?.id}`,
  };

  return (
    <UserDetailContext.Provider
      value={{
        isLoading,
        user,
        transactions,
        cards,
        recurrence,
        activityLogs,
        isLoadingActivityLogs,
        fetchActivityLogs,
      }}
    >
      <PageLayout
        breadcrumb={subscriptorBreadcrumb}
        title={fullname}
        isLoading={isLoading}
        titleRightSlot={
          <Heading size="xl" color="green.300">
            #{user?.id}
          </Heading>
        }
      >
        {children}
      </PageLayout>
    </UserDetailContext.Provider>
  );
}
