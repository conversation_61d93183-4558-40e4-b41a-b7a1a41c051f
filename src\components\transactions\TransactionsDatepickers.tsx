"use client";

import { Flex, FlexProps } from "@chakra-ui/react";
import { Datepicker } from "../common/Datepicker";
import { useTransactionsPage } from "@app/hooks/useTransactionsPage";
import { useTransactionsDatepickers } from "@app/hooks/useTransactionsDatepickers";

const datePickersWrapper: FlexProps = {
  alignItems: "center",
  justifyContent: "space-between",
  gap: "13px",
};

export function TransactionsDatepickers({ ...props }: FlexProps) {
  const { store, dispatch } = useTransactionsPage();
  const { onChangeDate } = useTransactionsDatepickers(store, dispatch);

  return (
    <Flex {...datePickersWrapper} {...props}>
      <Datepicker date={new Date()} onChange={(date) => onChangeDate(date, "from")} />
      <Datepicker date={new Date()} onChange={(date) => onChangeDate(date, "to")} />
    </Flex>
  );
}
