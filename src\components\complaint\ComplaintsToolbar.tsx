"use client";

import { useComplaintsPage } from "@app/contexts/ComplaintsPageProvider";
import { Flex, Select, Text } from "@chakra-ui/react";
import React, { ChangeEvent } from "react";
import { CheckButton } from "../common/CheckButton";
import { SupportStatus } from "@mainframe-peru/types/build/common";
import { common } from "@mainframe-peru/types";

export function ComplaintsToolbar() {
  const { filters, onSetStatus, onSetType } = useComplaintsPage();

  const onTogglerType = (e: ChangeEvent<HTMLSelectElement>) => {
    if (e.target.value === "") {
      onSetType(undefined);
      return;
    }
    onSetType(e.target.value as common.ComplaintType);
  };

  const onTogglerStatus = (status: SupportStatus) => {
    if (filters.status === status) {
      onSetStatus(undefined);
      return;
    }
    onSetStatus(status);
  };

  return (
    <Flex justifyContent={"space-between"} alignItems={"center"} flexWrap={"wrap"} mb={5}>
      <Flex gap={"15px"} justifyContent={"space-between"} w={"100%"} flexWrap={{ base: "wrap", md: "nowrap" }}>
        <Flex align={"center"} gap={5}>
          <Flex gap={"15px"} alignItems={"center"}>
            <CheckButton
              text="Abiertos"
              isActive={filters.status === "OPENED"}
              onClick={() => {
                onTogglerStatus("OPENED");
              }}
            />

            <CheckButton
              text="Cerrados"
              isActive={filters.status === "CLOSED"}
              onClick={() => {
                onTogglerStatus("CLOSED");
              }}
            />

            <CheckButton
              text="Archivados"
              isActive={filters.status === "ARCHIVED"}
              onClick={() => {
                onTogglerStatus("ARCHIVED");
              }}
            />
          </Flex>
        </Flex>
        <Flex gap={5} align={"center"}>
          <Text as={"small"}>Tipo</Text>
          <Select bg={"black"} color={"white"} onChange={onTogglerType} value={filters.type}>
            <option value="CLAIM">Reclamo</option>
            <option value="COMPLAINT">Queja</option>
            <option value="">Todos</option>
          </Select>
        </Flex>
      </Flex>
    </Flex>
  );
}
