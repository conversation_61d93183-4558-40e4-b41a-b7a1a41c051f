import { Box, BoxProps } from "@chakra-ui/react";

export function MasterCardIcon({ width = "38px", height = "23px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg width="100%" height="100%" viewBox="0 0 38 23" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.6316 20.049C16.6582 21.7125 14.0984 22.7166 11.3011 22.7166C5.0597 22.7166 0 17.7171 0 11.55C0 5.38277 5.0597 0.383286 11.3011 0.383286C14.0984 0.383286 16.6582 1.38745 18.6316 3.05086C20.605 1.38745 23.1649 0.383286 25.9621 0.383286C32.2036 0.383286 37.2632 5.38277 37.2632 11.55C37.2632 17.7171 32.2036 22.7166 25.9621 22.7166C23.1649 22.7166 20.605 21.7125 18.6316 20.049Z"
          fill="#ED0006"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.6316 20.049C21.0615 18.0009 22.6023 14.9532 22.6023 11.55C22.6023 8.14669 21.0615 5.09902 18.6316 3.05085C20.605 1.38745 23.1649 0.383286 25.9621 0.383286C32.2035 0.383286 37.2632 5.38277 37.2632 11.55C37.2632 17.7171 32.2035 22.7166 25.9621 22.7166C23.1649 22.7166 20.605 21.7125 18.6316 20.049Z"
          fill="#F9A000"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.6316 20.049C21.0615 18.0009 22.6023 14.9532 22.6023 11.55C22.6023 8.14673 21.0615 5.09908 18.6316 3.0509C16.2018 5.09908 14.661 8.14673 14.661 11.55C14.661 14.9532 16.2018 18.0009 18.6316 20.049Z"
          fill="#FF5E00"
        />
      </svg>
    </Box>
  );
}
