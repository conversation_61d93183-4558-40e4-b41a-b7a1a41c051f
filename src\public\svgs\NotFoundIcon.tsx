import { Box, BoxProps } from "@chakra-ui/react";

export function NotFoundIcon({ width = "150px", height = "150px", ...props }: BoxProps) {
  return (
    <Box width={width} height={height} {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 54 55" fill="none">
        <path d="M27 36.5L47 26.5V44.5L27 54.5V36.5Z" fill="#737373" />
        <path d="M27 36.5L7 26.5V44.5L27 54.5V36.5Z" fill="#404040" />
        <path d="M27 36.5L7 26.5L27 16.5L47 26.5L27 36.5Z" fill="#404040" />
        <path d="M34 5.5L27 16.5L47 26.5L54 15.5L34 5.5Z" fill="#737373" />
        <path d="M7 26.5L0 37.5L20 47.5L27 36.5L7 26.5Z" fill="#737373" />
        <path d="M30 49.5L44 42.5V39.5L30 46.5V49.5Z" fill="#404040" />
        <path d="M44 31.5V37.5L42 38.5V32.5L44 31.5Z" fill="#404040" />
        <path d="M41 37.5V33.5L39 34.5V38.5L41 37.5Z" fill="#404040" />
        <path d="M38 37.5V35.5L36 36.5V38.5L38 37.5Z" fill="#404040" />
        <path d="M38 35.5V33.5L36 34.5V36.5L38 35.5Z" fill="#00FF75" />
        <path d="M41 33.5V31.5L39 32.5V34.5L41 33.5Z" fill="#00FF75" />
        <path d="M44 31.5V29.5L42 30.5V32.5L44 31.5Z" fill="#00FF75" />
        <path d="M16 20L17 19.5V28L16 28.5V20Z" fill="#00FF75" />
        <path d="M39 0.5L40 0V5L39 5.5V0.5Z" fill="#00FF75" />
        <path d="M13 16L14 15.5V20L13 20.5V16Z" fill="#00FF75" />
        <path d="M47 26.5L27 36.5V16.5L47 26.5Z" fill="#262626" />
        <path d="M25 35.5L27 34.5L29 35.5L27 36.5L25 35.5Z" fill="#737373" />
        <path d="M19 10.3889L37 1.5V16.6111L19 25.5V10.3889Z" fill="#00FF75" />
        <path d="M31 16L29 17L29 12L32 10.5L32 8.5L29 10L29 8L34 5.5L34 11.5L31 13L31 16Z" fill="#262626" />
        <path d="M23 27.5V20.9545V19.3182L28 18.5L23 27.5Z" fill="#00FF75" />
        <path d="M25 10.5L27 9.5V14.5L24 16V18L27 16.5V18.5L22 21V15L25 13.5V10.5Z" fill="#262626" />
      </svg>
    </Box>
  );
}
