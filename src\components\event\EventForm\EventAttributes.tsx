import { Button } from "@app/components/common/Button";
import { Select } from "@app/components/common/Select";
import { useEventForm } from "@app/hooks/useEventForm";
import { AddIcon, MinusIcon } from "@chakra-ui/icons";
import {
  Checkbox,
  Grid,
  Heading,
  IconButton,
  Input,
  InputGroup,
  InputRightElement,
  Stack,
  Text,
} from "@chakra-ui/react";
import { common } from "@mainframe-peru/types";
import React from "react";

export function EventAttributeItem(props: { id: string; index: number }) {
  const { participationForm } = useEventForm();
  const [attribute, setAttribute] = React.useState<common.AttributeKey>(participationForm.current[props.index]);

  function changeAttribute(a: Partial<common.AttributeKey>) {
    const newAttr = {
      ...attribute,
      ...a,
    };
    setAttribute(newAttr);
    participationForm.current[props.index] = newAttr;
  }

  function changeAttributeOption(optionIndex: number, optionValue: string) {
    const options = attribute.options || [];
    const newOptions = [...options];
    newOptions.splice(optionIndex, 1, {
      id: options[optionIndex].id,
      value: optionValue,
    });
    changeAttribute({
      options: newOptions,
    });
  }

  function addAttributeOption() {
    const options = attribute.options || [];
    const newOptions = [
      ...options,
      {
        id: crypto.randomUUID(),
        value: `Opción ${options.length + 1}`,
      },
    ];
    changeAttribute({
      options: newOptions,
    });
  }

  function removeAttributeOption(optionIndex: number) {
    const options = attribute.options || [];
    const newOptions = [...options];
    newOptions.splice(optionIndex, 1);
    changeAttribute({
      options: newOptions,
    });
  }

  return (
    <Stack gap={5}>
      <Stack>
        <Text fontSize={13}>Texto</Text>
        <Input
          bgColor="#262626"
          border="1px solid gray.300"
          value={attribute.text}
          required
          onChange={(e) => changeAttribute({ text: e.target.value })}
          placeholder="Texto del campo"
          _placeholder={{ color: "gray.100" }}
        />
      </Stack>
      <Stack>
        <Text fontSize={13}>Descripción</Text>
        <Input
          bgColor="#262626"
          border="1px solid gray.300"
          value={attribute.description}
          onChange={(e) => changeAttribute({ description: e.target.value })}
          placeholder="Descripción del campo"
          _placeholder={{ color: "gray.100" }}
        />
      </Stack>
      <Stack>
        <Text fontSize={13}>Tipo</Text>
        <Select
          value={attribute.type}
          onChange={(e) => changeAttribute({ type: e as common.AttributeType })}
          options={[
            { value: "TEXT", text: "Texto" },
            { value: "MULTI", text: "Opción multiple" },
            { value: "SINGLE", text: "Selección" },
            { value: "FILE", text: "Archivo" },
          ]}
        />
      </Stack>
      <Stack>
        <Checkbox
          colorScheme="green"
          isChecked={!!attribute.required}
          onChange={() => changeAttribute({ required: !attribute.required })}
        >
          Obligatorio
        </Checkbox>
      </Stack>
      {(attribute.type === "MULTI" || attribute.type === "SINGLE") && (
        <Stack>
          <Text fontSize={13}>Opciones</Text>
          <Grid templateColumns="1fr 1fr 1fr" gap={4}>
            {attribute.options?.map((o, j) => (
              <InputGroup key={o.id}>
                <Input
                  key={o.id}
                  bgColor="#262626"
                  border="1px solid gray.300"
                  value={o.value}
                  required
                  onChange={(e) => changeAttributeOption(j, e.target.value)}
                  placeholder="Opción del campo"
                  _placeholder={{ color: "gray.100" }}
                />
                <InputRightElement>
                  <IconButton
                    aria-label="remove-attribute-option"
                    onClick={() => removeAttributeOption(j)}
                    icon={<MinusIcon />}
                  />
                </InputRightElement>
              </InputGroup>
            ))}
            <IconButton aria-label="add-attribute" onClick={addAttributeOption} icon={<AddIcon />} />
          </Grid>
        </Stack>
      )}
    </Stack>
  );
}

export function EventAttributes() {
  const { participationForm } = useEventForm();

  const [ids, setIds] = React.useState<string[]>(participationForm.current.map((p) => p.id));

  function addAttribute() {
    participationForm.current = [
      ...(participationForm.current || []),
      {
        id: crypto.randomUUID(),
        text: "",
        type: "TEXT",
        description: "",
        options: [],
        required: false,
      },
    ];
    setIds([...ids, crypto.randomUUID()]);
  }

  function removeAttribute(index: number) {
    participationForm.current.splice(index, 1);
    ids.splice(index, 1);
    setIds([...ids]);
  }

  return (
    <Stack>
      <Heading size={"md"} color={"blue.500"}>
        Formulario
      </Heading>
      <Grid templateColumns="repeat(3, 1fr)" gap={6}>
        {ids.map((id, i) => (
          <Stack key={id}>
            <EventAttributeItem id={id} index={i} />
            <Button onClick={() => removeAttribute(i)} variant="outline">
              Eliminar campo
            </Button>
          </Stack>
        ))}
        <Button
          onClick={addAttribute}
          height={ids.length ? "346px" : undefined}
          variant={ids.length ? "ghost" : "solid"}
        >
          Agregar campo
        </Button>
      </Grid>
    </Stack>
  );
}
