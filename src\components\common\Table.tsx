"use client";

import { NotFoundIcon } from "@app/public/svgs/NotFoundIcon";
import { ArrowLeftIcon, ArrowRightIcon } from "@chakra-ui/icons";
import {
  Box,
  BoxProps,
  Button,
  Table as ChakraTable,
  Flex,
  Heading,
  Spinner,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
} from "@chakra-ui/react";
import React from "react";

type TableProps = {
  title?: string;
  isLoading: boolean;
  headers: string[];
  rows: React.ReactNode[][];
  boxProps?: BoxProps;
  showButtons?: boolean;
  onNextPage?: () => void;
  onPrevPage?: () => void;
  offset?: number;
};

export function Table({
  title,
  headers,
  rows,
  isLoading = true,
  boxProps,
  showButtons = false,
  offset,
  onNextPage,
  onPrevPage,
}: TableProps) {
  return (
    <Box bgColor={"black"} padding={8} borderRadius={"16px"} {...boxProps}>
      {title && (
        <Heading textColor={"blue.500"} fontSize={"21px"} fontWeight={"bold"}>
          {title}
        </Heading>
      )}

      {isLoading && (
        <Flex
          bgColor={"black"}
          alignItems={"center"}
          justifyContent={"center"}
          minH={{
            base: "400px",
            md: "600px",
          }}
          position={"relative"}
        >
          <Spinner size={"lg"} />
        </Flex>
      )}

      {!isLoading && (
        <>
          <TableContainer minHeight={"100%"}>
            <ChakraTable>
              <Thead>
                <Tr key={"table-header"}>
                  {headers.map((header) => {
                    return <Th key={header}>{header}</Th>;
                  })}
                </Tr>
              </Thead>
              <Tbody>
                {rows.map((row, i) => {
                  return (
                    <Tr key={i}>
                      {row.map((field, j) => {
                        return <Td key={j}>{field}</Td>;
                      })}
                    </Tr>
                  );
                })}
              </Tbody>
            </ChakraTable>
          </TableContainer>
          {showButtons && (
            <Flex align="center" justify="flex-end" gap={3} pt={5}>
              <Button hidden={offset === 0} fontSize={13} leftIcon={<ArrowLeftIcon />} onClick={onPrevPage}>
                Anterior
              </Button>
              <Button hidden={rows.length < 100} fontSize={13} rightIcon={<ArrowRightIcon />} onClick={onNextPage}>
                Siguiente
              </Button>
            </Flex>
          )}
        </>
      )}

      {!isLoading && rows.length === 0 && (
        <Flex
          bgColor={"black"}
          flexDir={"column"}
          gap={5}
          alignItems={"center"}
          justifyContent={"center"}
          minH={{
            base: "400px",
            md: "600px",
          }}
          position={"relative"}
        >
          <NotFoundIcon />
          <Text>No se encontraron resultados</Text>
        </Flex>
      )}
    </Box>
  );
}
