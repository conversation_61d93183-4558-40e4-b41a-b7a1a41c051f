"use client";

import { useSession } from "@app/hooks/useSession";
import { NavigationGroup } from "@app/lib/types";
import { CardIcon, GameControlIcon, UserIcon } from "@app/public/svgs";
import { BookIcon } from "@app/public/svgs/BookIcon";
import { EventsIcon } from "@app/public/svgs/EventsIcon";
import { Box, BoxProps, Divider, Flex, FlexProps, Text } from "@chakra-ui/react";
import { NavList } from "@components/common/NavList";
import Image from "next/image";
import React from "react";

const flexContainerProps: FlexProps = {
  margin: "auto",
  width: 144,
  height: 144,
  bgColor: "gray.300",
  rounded: "100%",
  justifyContent: "center",
  alignItems: "center",
};

export function AppAside({ ...asideProps }: BoxProps) {
  const session = useSession();

  const navigationMenu: NavigationGroup[] = [
    {
      divider: true,
      title: "MÉTRICAS",
      items: [
        {
          to: `/${session.admin?.influencerId}/app/dashboard`,
          text: "Dashboard",
          Icon: <GameControlIcon />,
          show: true,
        },
      ],
    },
    {
      divider: true,
      title: "ADMINISTRACIÓN",
      items: [
        {
          to: `/${session.admin?.influencerId}/app/pagos`,
          text: "Pagos",
          Icon: <CardIcon />,
          show: session.isInPolicy("transaction", "LIST_TRANSACTIONS"),
        },
        {
          to: `/${session.admin?.influencerId}/app/usuarios`,
          text: "Usuarios",
          Icon: <UserIcon />,
          show: session.isInPolicy("user", "LIST_USERS"),
        },
        {
          to: `/${session.admin?.influencerId}/app/premios-y-eventos`,
          text: "Premios y Eventos",
          Icon: <EventsIcon />,
          show: session.isInPolicy("event", "LIST_EVENTS"),
        },
        {
          to: `/${session.admin?.influencerId}/app/quejas-y-reclamos`,
          text: "Quejas y Reclamos",
          Icon: <BookIcon />,
          show: session.isInPolicy("complaint", "LIST_ISSUES"),
        },
        {
          to: `/${session.admin?.influencerId}/app/admins`,
          text: "Admins",
          Icon: <UserIcon />,
          show: session.isInPolicy("admin", "LIST_ADMINS"),
        },
        {
          to: `/${session.admin?.influencerId}/app/config`,
          text: "Configuración",
          Icon: <UserIcon />,
          show: session.isInPolicy("influencer", "UPDATE_INFLUENCER"),
        },
      ],
    },
    {
      divider: false,
      title: "PROMOCIONES",
      items: [
        {
          to: `/${session.admin?.influencerId}/app/promociones/negocios`,
          text: "Negocios",
          Icon: <BookIcon />,
          show: session.isInPolicy("business", "LIST_BUSINESS"),
        },
        {
          to: `/${session.admin?.influencerId}/app/promociones/promociones`,
          text: "Promociones",
          Icon: <EventsIcon />,
          show: session.isInPolicy("businessPromotion", "LIST_PROMOTION"),
        },
      ],
    },
  ];

  return (
    <Box {...asideProps} as="aside">
      <Box pt={"40px"}>
        <Flex {...flexContainerProps}>
          {<Image src={session.influencer.logoUrl || ""} alt="logo" width={100} height={100} />}
        </Flex>
        <Flex justifyContent={"center"} mt={"30px"}>
          <Flex flexDir={"column"} as="nav">
            {navigationMenu.map(({ title, items, divider }) => (
              <React.Fragment key={title}>
                <NavList title={title} items={items} />
                {divider && <Divider my="25px" borderColor={"gray.300"} />}
              </React.Fragment>
            ))}
          </Flex>
        </Flex>
      </Box>
      <Text fontSize={"12px"} py={"15px"}>
        Powered by &copy; <strong>MAINFRAME. {new Date().getFullYear()}</strong>
      </Text>
    </Box>
  );
}
