export interface ActivityLogItem {
  sk: number;
  pk: string;
  request: string;
  influencerId: string;
  clientId: number;
  type: string;
  message: string;
  createdAt: number;
  clientType: string;
  status: "SUCCESS" | "FAILED";
  gsi1pk: string;
  gsi1sk: number;
  gsi2pk: string;
  gsi2sk: string;
  gsi3pk: string;
  gsi3sk: string;
}

export interface ListActivityLogResponse {
  items: ActivityLogItem[];
  count: number;
}
