import { common } from "@mainframe-peru/types";
import { Tag } from "../common/Tag";

export function IssueTypeTag({ type }: { type: common.ComplaintType }) {
  const state: { [k: string]: { colorScheme: string; text: string } } = {
    CLAIM: {
      colorScheme: "orange",
      text: "<PERSON><PERSON><PERSON><PERSON>",
    },
    COMPLAINT: {
      colorScheme: "red",
      text: "Queja",
    },
  };
  return (
    <Tag colorScheme={state[type || "CLAIM"].colorScheme} w={"fit-content"}>
      {state[type || "CLAIM"].text}
    </Tag>
  );
}
