import { EventStatus } from "@mainframe-peru/types/build/common";
import { TagMaper } from "./EventTypeTag";
import { Tag } from "../common/Tag";

const eventStatusMapper: TagMaper<EventStatus> = {
  ACTIVE: {
    colorScheme: "green",
    text: "Activo",
  },
  INACTIVE: {
    colorScheme: "red",
    text: "Inactivo",
  },
};

export function EventStatusTag({ status }: { status: EventStatus }) {
  return <Tag colorScheme={eventStatusMapper[status].colorScheme}>{eventStatusMapper[status].text}</Tag>;
}
