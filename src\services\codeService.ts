import { BaseService } from "@app/lib/service/baseService";
import { businessPromotionCode } from "@mainframe-peru/types";

export class CodeService extends BaseService {
  static async listCodes(
    query: Partial<businessPromotionCode.ListBusinessPromotionCodeRequest>,
  ): Promise<businessPromotionCode.ListBusinessPromotionCodeResponse> {
    const url = `${this.BASE_URL}/business-promotion-code/list?${this.buildQueryParams(query)}`;
    const { data } = await this.fetchData<businessPromotionCode.ListBusinessPromotionCodeResponse>(url, {
      credentials: "include",
    });
    return data;
  }

  static async createCode(
    payload: businessPromotionCode.CreateBusinessPromotionCodeRequest,
  ): Promise<businessPromotionCode.CreateBusinessPromotionCodeResponse> {
    const { data } = await this.fetchData<businessPromotionCode.CreateBusinessPromotionCodeResponse>(
      `${this.BASE_URL}/business-promotion-code/`,
      {
        method: "POST",
        credentials: "include",
        body: JSON.stringify(payload),
      },
    );
    return data;
  }

  static async deleteCode(payload: businessPromotionCode.DeleteBusinessPromotionCodeRequest) {
    const response = await this.fetchData(`${this.BASE_URL}/business-promotion-code/delete`, {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    return response;
  }

  static async uploadCodes(payload: businessPromotionCode.UploadPromotionCodesRequest) {
    const response = await this.fetchData(`${this.BASE_URL}/business-promotion-code/upload`, {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    return response;
  }
}
