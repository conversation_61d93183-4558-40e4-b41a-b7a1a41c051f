"use client";

import { Box } from "@chakra-ui/react";
import { usePromotionPage } from "@app/contexts/PromotionPageProvider";
import { businessPromotion } from "@mainframe-peru/types";
import { Table } from "../common/Table";
import { useSession } from "@app/hooks/useSession";
import Link from "next/link";
import { EditIcon } from "@chakra-ui/icons";

export function PromotionsTable() {
  const { promotions, isLoading } = usePromotionPage();

  const makeRows = (promotions: businessPromotion.GetBusinessPromotionResponse[]) => {
    return promotions.map((promotion) => {
      const { id, name, type, value } = promotion;

      return [
        id,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {name}
        </Box>,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {promotion.business.name}
        </Box>,
        <Box key={id} overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap" maxWidth={200}>
          {type}
        </Box>,
        value,
        <EditRow key={id} id={id} />,
      ];
    });
  };

  return (
    <Table
      isLoading={isLoading}
      rows={makeRows(promotions)}
      boxProps={{ mt: 34 }}
      headers={["Id", "Nombre", "Negocio", "Tipo", "Valor", ""]}
    />
  );
}

function EditRow({ id }: { id: number }) {
  const session = useSession();
  return (
    <Link href={`/${session.admin?.influencerId}/app/promociones/promociones/editar/?id=${id}`}>
      <EditIcon />
    </Link>
  );
}
