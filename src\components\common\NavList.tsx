import { NavigationItem } from "@app/lib/types";
import { Text, UnorderedList } from "@chakra-ui/react";
import { NavItem as ChakraNavItem } from "./NavItem";

type NavigationListProps = {
  title?: string;
  items: NavigationItem[];
};

export function NavList({ title, items }: NavigationListProps) {
  return (
    <UnorderedList listStyleType={"none"} margin={0} display={"flex"} flexDir={"column"} gap={2}>
      {title && (
        <Text as={"h3"} letterSpacing={"1.1px"} textTransform={"uppercase"} fontSize={8} color={"green.300"}>
          {title}
        </Text>
      )}
      {items
        .filter((navItem) => navItem.show)
        .map((navItem, i) => (
          <ChakraNavItem key={i} {...navItem} />
        ))}
    </UnorderedList>
  );
}
