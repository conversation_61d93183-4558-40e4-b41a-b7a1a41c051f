"use client";

import { Button } from "@app/components/common/Button";
import { usePromotionPage } from "@app/contexts/PromotionPageProvider";
import { useSession } from "@app/hooks/useSession";
import { PromotionService } from "@app/services/promotionService";
import { Flex, Heading } from "@chakra-ui/react";
import { useRouter } from "next/navigation";
import { useState } from "react";

export function PromotionToolbar() {
  const router = useRouter();
  const session = useSession();
  const { businessId } = usePromotionPage();

  const [loadingInvalidate, setLoadingInvalidate] = useState(false);

  const onInvalidateClick = async () => {
    setLoadingInvalidate(true);
    await PromotionService.invalidateCache();
    setLoadingInvalidate(false);
  };

  return (
    <Flex justifyContent={"space-between"} mb={4} flexDirection={["column", null, "row"]}>
      <Flex alignItems={"center"} justify={"space-between"} mb={[3, null, 0]}>
        <Heading size={"md"} color={"#5C73F2"}>
          Promociones
        </Heading>
      </Flex>
      <Flex flexDirection={["column", null, "row"]}>
        {session.isInPolicy("businessPromotion", "PUT_PROMOTION") && (
          <Button variant="outline" mr={2} isLoading={loadingInvalidate} onClick={onInvalidateClick} mb={[3, null, 0]}>
            Refrescar contenido
          </Button>
        )}
        <Button
          onClick={() =>
            router.push(
              `/${session.admin?.influencerId}/app/promociones/promociones/crear/${businessId ? `?businessId=${businessId}` : ""}`,
            )
          }
        >
          Crear promoción
        </Button>
      </Flex>
    </Flex>
  );
}
